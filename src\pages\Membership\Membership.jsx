import { membershipPlans } from '../../data/mockData';
import { useAuth } from '../../contexts/AuthContext';
import Button from '../../components/UI/Button/Button';
import Card from '../../components/UI/Card/Card';
import styles from './Membership.module.css';

const Membership = () => {
  const { user, isAuthenticated } = useAuth();

  return (
    <div className={styles.membership}>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>Choose Your Membership</h1>
          <p className={styles.subtitle}>
            Select the perfect plan to achieve your fitness goals
          </p>
        </div>

        <div className={styles.plansGrid}>
          {membershipPlans.map(plan => (
            <Card 
              key={plan.id} 
              hover 
              className={`${styles.planCard} ${plan.popular ? styles.popular : ''}`}
            >
              {plan.popular && (
                <div className={styles.popularBadge}>Most Popular</div>
              )}
              
              <div className={styles.planHeader}>
                <h3 className={styles.planName}>{plan.name}</h3>
                <div className={styles.planPrice}>
                  <span className={styles.price}>${plan.price}</span>
                  <span className={styles.billing}>/{plan.billing}</span>
                </div>
              </div>

              <div className={styles.planFeatures}>
                <h4 className={styles.featuresTitle}>What's included:</h4>
                <ul className={styles.featuresList}>
                  {plan.features.map((feature, index) => (
                    <li key={index} className={styles.feature}>
                      <span className={styles.checkIcon}>✓</span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              <div className={styles.planActions}>
                {isAuthenticated && user?.membershipType === plan.id ? (
                  <Button variant="success" fullWidth disabled>
                    Current Plan
                  </Button>
                ) : (
                  <Button 
                    fullWidth 
                    variant={plan.popular ? 'primary' : 'outline'}
                  >
                    {isAuthenticated ? 'Upgrade' : 'Get Started'}
                  </Button>
                )}
              </div>
            </Card>
          ))}
        </div>

        <div className={styles.benefits}>
          <h2 className={styles.benefitsTitle}>Why Choose FitnessGym?</h2>
          <div className={styles.benefitsGrid}>
            <div className={styles.benefit}>
              <div className={styles.benefitIcon}>🏋️‍♂️</div>
              <h3 className={styles.benefitTitle}>Premium Equipment</h3>
              <p className={styles.benefitText}>
                State-of-the-art fitness equipment from leading brands
              </p>
            </div>
            <div className={styles.benefit}>
              <div className={styles.benefitIcon}>👨‍💼</div>
              <h3 className={styles.benefitTitle}>Expert Trainers</h3>
              <p className={styles.benefitText}>
                Certified personal trainers to guide your fitness journey
              </p>
            </div>
            <div className={styles.benefit}>
              <div className={styles.benefitIcon}>🏃‍♀️</div>
              <h3 className={styles.benefitTitle}>Group Classes</h3>
              <p className={styles.benefitText}>
                Diverse range of group fitness classes for all levels
              </p>
            </div>
            <div className={styles.benefit}>
              <div className={styles.benefitIcon}>📱</div>
              <h3 className={styles.benefitTitle}>Mobile App</h3>
              <p className={styles.benefitText}>
                Track progress and book sessions on the go
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Membership;
