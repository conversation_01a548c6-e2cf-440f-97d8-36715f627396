import styles from './Button.module.css';

const Button = ({
  children,
  as: Component = 'button',
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  fullWidth = false,
  onClick,
  type = 'button',
  className = '',
  ...props
}) => {
  const buttonClasses = [
    styles.button,
    styles[variant],
    styles[size],
    fullWidth && styles.fullWidth,
    loading && styles.loading,
    className
  ].filter(Boolean).join(' ');

  const componentProps = {
    className: buttonClasses,
    onClick,
    ...props
  };

  // Only add type and disabled for actual button elements
  if (Component === 'button') {
    componentProps.type = type;
    componentProps.disabled = disabled || loading;
  }

  return (
    <Component {...componentProps}>
      {loading && (
        <div className={styles.spinner}>
          <svg className={styles.spinnerIcon} viewBox="0 0 24 24">
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
              fill="none"
              strokeLinecap="round"
              strokeDasharray="32"
              strokeDashoffset="32"
            >
              <animate
                attributeName="stroke-dasharray"
                dur="2s"
                values="0 32;16 16;0 32;0 32"
                repeatCount="indefinite"
              />
              <animate
                attributeName="stroke-dashoffset"
                dur="2s"
                values="0;-16;-32;-32"
                repeatCount="indefinite"
              />
            </circle>
          </svg>
        </div>
      )}
      <span className={loading ? styles.hiddenText : ''}>{children}</span>
    </Component>
  );
};

export default Button;
