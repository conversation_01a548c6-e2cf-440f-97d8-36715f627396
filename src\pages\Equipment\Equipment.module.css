.equipment {
  min-height: 100vh;
  background: var(--gray-50);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-6) var(--space-3);
}

/* Header Section */
.header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.125rem;
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Filters */
.filters {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-8);
  justify-content: center;
  flex-wrap: wrap;
}

.filterBtn {
  padding: var(--space-3) var(--space-6);
  border: 2px solid var(--gray-300);
  background: white;
  color: var(--gray-700);
  border-radius: var(--radius-full);
  font-weight: 500;
  transition: all var(--transition-fast);
  cursor: pointer;
  min-height: 48px;
  font-size: 0.875rem;
}

.filterBtn:hover {
  border-color: var(--primary-500);
  color: var(--primary-600);
  transform: translateY(-1px);
}

.filterBtn.active {
  background: var(--primary-500);
  border-color: var(--primary-500);
  color: white;
}

/* Equipment Grid */
.equipmentGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-6);
}

.equipmentCard {
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
}

.equipmentCard:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.equipmentImage {
  width: 100%;
  height: 200px;
  object-fit: cover;
  background: var(--gray-100);
}

.equipmentInfo {
  padding: var(--space-6);
}

.equipmentHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
  gap: var(--space-3);
}

.equipmentName {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
  flex: 1;
}

.status {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  flex-shrink: 0;
}

.status.success {
  background: var(--success-100);
  color: var(--success-700);
}

.status.warning {
  background: var(--warning-100);
  color: var(--warning-700);
}

.status.error {
  background: var(--error-100);
  color: var(--error-700);
}

.status.default {
  background: var(--gray-100);
  color: var(--gray-700);
}

.equipmentDetails {
  margin-bottom: var(--space-4);
}

.brand {
  font-size: 0.875rem;
  color: var(--gray-600);
  margin-bottom: var(--space-2);
}

.location {
  font-size: 0.875rem;
  color: var(--gray-600);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

/* Features */
.features {
  margin-bottom: var(--space-6);
}

.featuresTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-3);
}

.featuresList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.feature {
  font-size: 0.875rem;
  color: var(--gray-600);
  position: relative;
  padding-left: var(--space-4);
}

.feature::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--success-500);
  font-weight: 600;
}

/* Maintenance */
.maintenance {
  border-top: 1px solid var(--gray-200);
  padding-top: var(--space-4);
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.maintenanceItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-3);
}

.label {
  font-size: 0.75rem;
  color: var(--gray-500);
  font-weight: 500;
}

.value {
  font-size: 0.75rem;
  color: var(--gray-700);
  font-weight: 600;
}

/* Responsive Design */
@media (min-width: 768px) {
  .container {
    padding: var(--space-8) var(--space-4);
  }
  
  .title {
    font-size: 2.5rem;
  }
  
  .equipmentGrid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
  
  .filters {
    gap: var(--space-4);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: var(--space-12) var(--space-6);
  }
  
  .title {
    font-size: 3rem;
  }
  
  .equipmentGrid {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: var(--space-8);
  }
}

@media (max-width: 767px) {
  .equipmentGrid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .equipmentHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  .status {
    align-self: flex-start;
  }
  
  .maintenanceItem {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }
  
  .filters {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: var(--space-2);
  }
  
  .filterBtn {
    flex-shrink: 0;
    padding: var(--space-2) var(--space-4);
    font-size: 0.8rem;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .equipmentCard,
  .filterBtn {
    border: 2px solid #000;
  }
  
  .title {
    -webkit-text-fill-color: #000;
    color: #000;
  }
  
  .status {
    border: 1px solid #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .equipmentCard,
  .filterBtn {
    transition: none;
    transform: none;
  }
  
  .equipmentCard:hover {
    transform: none;
  }
}
