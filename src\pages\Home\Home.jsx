import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { trainers, classes } from '../../data/mockData';
import Button from '../../components/UI/Button/Button';
import Card from '../../components/UI/Card/Card';
import styles from './Home.module.css';

const Home = () => {
  const { isAuthenticated } = useAuth();

  const featuredTrainers = trainers.slice(0, 3);
  const upcomingClasses = classes.slice(0, 3);

  const features = [
    {
      icon: '🏋️‍♂️',
      title: 'State-of-the-Art Equipment',
      description: 'Access to the latest fitness equipment and technology to maximize your workout efficiency.'
    },
    {
      icon: '👨‍💼',
      title: 'Expert Trainers',
      description: 'Work with certified personal trainers who will guide you towards your fitness goals.'
    },
    {
      icon: '🎥',
      title: 'Live Training Sessions',
      description: 'Join real-time training sessions with interactive coaching and community support.'
    },
    {
      icon: '📊',
      title: 'Progress Tracking',
      description: 'Monitor your fitness journey with detailed analytics and personalized insights.'
    },
    {
      icon: '🥗',
      title: 'Nutrition Guidance',
      description: 'Get customized meal plans and nutrition advice to complement your fitness routine.'
    },
    {
      icon: '🏃‍♀️',
      title: 'Group Classes',
      description: 'Join energizing group fitness classes for motivation and community connection.'
    }
  ];

  const stats = [
    { number: '500+', label: 'Active Members' },
    { number: '50+', label: 'Expert Trainers' },
    { number: '100+', label: 'Group Classes' },
    { number: '24/7', label: 'Gym Access' }
  ];

  return (
    <div className={styles.home}>
      {/* Hero Section */}
      <section className={styles.hero}>
        <div className={styles.heroContent}>
          <div className={styles.heroText}>
            <h1 className={styles.heroTitle}>
              Transform Your Body,
              <span className={styles.heroTitleAccent}> Transform Your Life</span>
            </h1>
            <p className={styles.heroDescription}>
              Join our premium fitness community and unlock your potential with expert trainers, 
              cutting-edge equipment, and personalized workout plans designed just for you.
            </p>
            <div className={styles.heroActions}>
              {isAuthenticated ? (
                <>
                  <Button as={Link} to="/dashboard" size="large">
                    Go to Dashboard
                  </Button>
                  <Button as={Link} to="/live-sessions" variant="outline" size="large">
                    Start Live Session
                  </Button>
                </>
              ) : (
                <>
                  <Button as={Link} to="/register" size="large">
                    Start Your Journey
                  </Button>
                  <Button as={Link} to="/membership" variant="outline" size="large">
                    View Membership Plans
                  </Button>
                </>
              )}
            </div>
          </div>
          <div className={styles.heroImage}>
            <img
              src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop"
              alt="Fitness training"
              className={styles.heroImg}
            />
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className={styles.stats}>
        <div className={styles.container}>
          <div className={styles.statsGrid}>
            {stats.map((stat, index) => (
              <div key={index} className={styles.statItem}>
                <div className={styles.statNumber}>{stat.number}</div>
                <div className={styles.statLabel}>{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className={styles.features}>
        <div className={styles.container}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>Why Choose FitnessGym?</h2>
            <p className={styles.sectionDescription}>
              We provide everything you need to achieve your fitness goals in a supportive, 
              high-energy environment.
            </p>
          </div>
          <div className={styles.featuresGrid}>
            {features.map((feature, index) => (
              <Card key={index} hover className={styles.featureCard}>
                <div className={styles.featureIcon}>{feature.icon}</div>
                <h3 className={styles.featureTitle}>{feature.title}</h3>
                <p className={styles.featureDescription}>{feature.description}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Trainers */}
      <section className={styles.trainers}>
        <div className={styles.container}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>Meet Our Expert Trainers</h2>
            <p className={styles.sectionDescription}>
              Work with certified professionals who are passionate about helping you succeed.
            </p>
          </div>
          <div className={styles.trainersGrid}>
            {featuredTrainers.map((trainer) => (
              <Card key={trainer.id} hover className={styles.trainerCard}>
                <img
                  src={trainer.avatar}
                  alt={trainer.name}
                  className={styles.trainerImage}
                  onError={(e) => {
                    e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(trainer.name)}&background=667eea&color=fff&size=300`;
                  }}
                />
                <div className={styles.trainerInfo}>
                  <h3 className={styles.trainerName}>{trainer.name}</h3>
                  <div className={styles.trainerSpecialties}>
                    {trainer.specialties.slice(0, 2).map((specialty, index) => (
                      <span key={index} className={styles.specialtyTag}>
                        {specialty}
                      </span>
                    ))}
                  </div>
                  <div className={styles.trainerRating}>
                    <span className={styles.rating}>⭐ {trainer.rating}</span>
                    <span className={styles.reviews}>({trainer.reviewCount} reviews)</span>
                  </div>
                  <Button as={Link} to={`/trainers/${trainer.id}`} fullWidth>
                    View Profile
                  </Button>
                </div>
              </Card>
            ))}
          </div>
          <div className={styles.sectionFooter}>
            <Button as={Link} to="/trainers" variant="outline" size="large">
              View All Trainers
            </Button>
          </div>
        </div>
      </section>

      {/* Upcoming Classes */}
      <section className={styles.classes}>
        <div className={styles.container}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>Popular Classes</h2>
            <p className={styles.sectionDescription}>
              Join our energizing group fitness classes and train with like-minded individuals.
            </p>
          </div>
          <div className={styles.classesGrid}>
            {upcomingClasses.map((classItem) => (
              <Card key={classItem.id} hover className={styles.classCard}>
                <div className={styles.classHeader}>
                  <h3 className={styles.className}>{classItem.name}</h3>
                  <span className={styles.classDifficulty}>{classItem.difficulty}</span>
                </div>
                <p className={styles.classDescription}>{classItem.description}</p>
                <div className={styles.classDetails}>
                  <div className={styles.classDetail}>
                    <span className={styles.detailLabel}>Duration:</span>
                    <span className={styles.detailValue}>{classItem.duration} min</span>
                  </div>
                  <div className={styles.classDetail}>
                    <span className={styles.detailLabel}>Capacity:</span>
                    <span className={styles.detailValue}>
                      {classItem.currentEnrollment}/{classItem.maxCapacity}
                    </span>
                  </div>
                  <div className={styles.classDetail}>
                    <span className={styles.detailLabel}>Price:</span>
                    <span className={styles.detailValue}>${classItem.price}</span>
                  </div>
                </div>
                <Button as={Link} to={`/classes/${classItem.id}`} fullWidth>
                  Book Class
                </Button>
              </Card>
            ))}
          </div>
          <div className={styles.sectionFooter}>
            <Button as={Link} to="/classes" variant="outline" size="large">
              View All Classes
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className={styles.cta}>
        <div className={styles.container}>
          <Card variant="gradient" padding="large" className={styles.ctaCard}>
            <div className={styles.ctaContent}>
              <h2 className={styles.ctaTitle}>Ready to Start Your Fitness Journey?</h2>
              <p className={styles.ctaDescription}>
                Join thousands of members who have transformed their lives with our comprehensive 
                fitness programs and expert guidance.
              </p>
              <div className={styles.ctaActions}>
                {isAuthenticated ? (
                  <Button as={Link} to="/live-sessions" size="large" variant="secondary">
                    Start Training Now
                  </Button>
                ) : (
                  <>
                    <Button as={Link} to="/register" size="large" variant="secondary">
                      Join Now
                    </Button>
                    <Button as={Link} to="/contact" variant="outline" size="large">
                      Contact Us
                    </Button>
                  </>
                )}
              </div>
            </div>
          </Card>
        </div>
      </section>
    </div>
  );
};

export default Home;
