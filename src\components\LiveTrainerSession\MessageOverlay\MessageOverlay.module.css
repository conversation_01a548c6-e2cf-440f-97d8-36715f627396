.messageOverlay {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 400px;
  max-height: 600px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  z-index: 1000;
  transition: all 0.3s ease;
}

.messageOverlay.minimized {
  max-height: 60px;
  overflow: hidden;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 16px 16px 0 0;
}

.headerInfo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.connectionStatus {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.connectionDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #6b7280;
}

.connectionStatus.connected .connectionDot {
  background: #10b981;
  animation: pulse 2s infinite;
}

.connectionStatus.connecting .connectionDot {
  background: #f59e0b;
  animation: pulse 1s infinite;
}

.connectionText {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

.headerActions {
  display: flex;
  gap: 0.5rem;
}

.headerBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: #6b7280;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.headerBtn:hover {
  background: #e5e7eb;
  color: #374151;
}

.icon {
  width: 1rem;
  height: 1rem;
}

.messagesContainer {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 400px;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #6b7280;
}

.emptyIcon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.emptyText {
  font-size: 0.875rem;
  margin: 0;
}

.message {
  display: flex;
  margin-bottom: 0.5rem;
}

.trainerMessage {
  justify-content: flex-start;
}

.clientMessage {
  justify-content: flex-end;
}

.messageContent {
  max-width: 80%;
  display: flex;
  flex-direction: column;
}

.trainerMessage .messageContent {
  align-items: flex-start;
}

.clientMessage .messageContent {
  align-items: flex-end;
}

.messageText {
  padding: 0.75rem 1rem;
  border-radius: 18px;
  font-size: 0.875rem;
  line-height: 1.4;
  word-wrap: break-word;
}

.trainerMessage .messageText {
  background: #f3f4f6;
  color: #1f2937;
  border-bottom-left-radius: 6px;
}

.clientMessage .messageText {
  background: #667eea;
  color: white;
  border-bottom-right-radius: 6px;
}

.messageInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.25rem;
  padding: 0 0.5rem;
}

.messageTime {
  font-size: 0.625rem;
  color: #9ca3af;
}

.messageStatus {
  font-size: 0.625rem;
}

.messageStatus.sending {
  color: #f59e0b;
}

.messageStatus.delivered {
  color: #10b981;
}

.messageStatus.read {
  color: #3b82f6;
}

.typingIndicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem 1rem;
  background: #f3f4f6;
  border-radius: 18px;
  border-bottom-left-radius: 6px;
}

.typingDot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #9ca3af;
  animation: typingAnimation 1.4s infinite ease-in-out;
}

.typingDot:nth-child(2) {
  animation-delay: 0.2s;
}

.typingDot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingAnimation {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

.quickResponses {
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  max-height: 120px;
  overflow-y: auto;
}

.quickResponseBtn {
  padding: 0.5rem 0.75rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 16px;
  font-size: 0.75rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.quickResponseBtn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.inputContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 0 0 16px 16px;
}

.quickResponseToggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid #d1d5db;
  background: white;
  color: #6b7280;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.quickResponseToggle:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.inputForm {
  display: flex;
  flex: 1;
  gap: 0.5rem;
}

.messageInput {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s ease;
}

.messageInput:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.messageInput:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.sendBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.sendBtn:hover:not(:disabled) {
  background: #5a67d8;
  transform: scale(1.05);
}

.sendBtn:disabled {
  background: #d1d5db;
  cursor: not-allowed;
  transform: none;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .messageOverlay {
    bottom: 1rem;
    right: 1rem;
    left: 1rem;
    width: auto;
    max-height: 70vh;
  }
  
  .messages {
    max-height: 300px;
  }
  
  .quickResponses {
    max-height: 80px;
  }
}

@media (max-width: 480px) {
  .messageOverlay {
    bottom: 0;
    right: 0;
    left: 0;
    border-radius: 16px 16px 0 0;
    max-height: 80vh;
  }
  
  .header {
    padding: 0.75rem 1rem;
  }
  
  .title {
    font-size: 0.875rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .messageOverlay,
  .headerBtn,
  .quickResponseBtn,
  .sendBtn,
  .typingDot,
  .connectionDot {
    transition: none;
    animation: none;
  }
}

/* Focus styles for accessibility */
.headerBtn:focus,
.quickResponseBtn:focus,
.quickResponseToggle:focus,
.sendBtn:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.headerBtn:focus:not(:focus-visible),
.quickResponseBtn:focus:not(:focus-visible),
.quickResponseToggle:focus:not(:focus-visible),
.sendBtn:focus:not(:focus-visible) {
  outline: none;
}
