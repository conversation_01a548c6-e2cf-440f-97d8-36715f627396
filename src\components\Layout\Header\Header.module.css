.header {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
  color: white;
  box-shadow: var(--shadow-lg);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

/* Logo */
.logo {
  flex-shrink: 0;
}

.logoLink {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  color: white;
  text-decoration: none;
  transition: opacity var(--transition-fast);
}

.logoLink:hover {
  opacity: 0.9;
}

.logoIcon {
  font-size: 2rem;
  line-height: 1;
}

.logoText {
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: -0.025em;
}

/* Desktop Navigation */
.desktopNav {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.navLink {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-weight: 500;
  font-size: 0.875rem;
}

.navLink:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.navLink.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.navIcon {
  font-size: 1.125rem;
  line-height: 1;
}

.navText {
  white-space: nowrap;
}

/* User Actions */
.userActions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.notificationBtn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: var(--radius-md);
  color: white;
  cursor: pointer;
  transition: background var(--transition-fast);
}

.notificationBtn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.notificationBadge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: var(--error-500);
  color: white;
  font-size: 0.625rem;
  font-weight: 700;
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  line-height: 1.2;
}

/* Profile Menu */
.profileMenu {
  position: relative;
}

.profileBtn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: var(--radius-md);
  padding: var(--space-2);
  color: white;
  cursor: pointer;
  transition: background var(--transition-fast);
}

.profileBtn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.userName {
  font-weight: 500;
  font-size: 0.875rem;
}

.chevron {
  width: 1rem;
  height: 1rem;
  transition: transform var(--transition-fast);
}

.profileBtn:hover .chevron {
  transform: rotate(180deg);
}

.profileDropdown {
  position: absolute;
  top: calc(100% + var(--space-2));
  right: 0;
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--gray-200);
  min-width: 280px;
  overflow: hidden;
  z-index: var(--z-dropdown);
}

.profileHeader {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
}

.dropdownAvatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-500);
}

.profileInfo {
  flex: 1;
  min-width: 0;
}

.profileName {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-1);
}

.profileEmail {
  font-size: 0.875rem;
  color: var(--gray-600);
  word-break: break-word;
}

.profileActions {
  padding: var(--space-2);
}

.profileAction {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  width: 100%;
  padding: var(--space-3);
  background: none;
  border: none;
  border-radius: var(--radius-md);
  color: var(--gray-700);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background var(--transition-fast);
}

.profileAction:hover {
  background: var(--gray-100);
  color: var(--gray-900);
}

.actionIcon {
  width: 1.125rem;
  height: 1.125rem;
  color: var(--gray-500);
}

/* Auth Actions */
.authActions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.loginBtn {
  padding: var(--space-2) var(--space-4);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-md);
  color: white;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.loginBtn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.registerBtn {
  padding: var(--space-2) var(--space-4);
  background: white;
  border: 1px solid white;
  border-radius: var(--radius-md);
  color: var(--primary-500);
  text-decoration: none;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.registerBtn:hover {
  background: var(--gray-100);
  transform: translateY(-1px);
}

/* Mobile Menu Button */
.mobileMenuBtn {
  display: none;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: var(--radius-md);
  color: white;
  cursor: pointer;
  transition: background var(--transition-fast);
}

.mobileMenuBtn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.icon {
  width: 1.5rem;
  height: 1.5rem;
}

/* Mobile Navigation */
.mobileNav {
  display: none;
  flex-direction: column;
  background: white;
  border-top: 1px solid var(--gray-200);
  box-shadow: var(--shadow-lg);
}

.mobileNavLink {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  color: var(--gray-700);
  text-decoration: none;
  border-bottom: 1px solid var(--gray-100);
  transition: background var(--transition-fast);
}

.mobileNavLink:hover {
  background: var(--gray-50);
}

.mobileNavLink.active {
  background: var(--primary-50);
  color: var(--primary-700);
  border-left: 4px solid var(--primary-500);
}

.mobileAuthActions {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  padding: var(--space-4);
  border-top: 1px solid var(--gray-200);
}

.mobileLoginBtn,
.mobileRegisterBtn {
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: 600;
  text-align: center;
  transition: all var(--transition-fast);
}

.mobileLoginBtn {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
}

.mobileLoginBtn:hover {
  background: var(--gray-200);
}

.mobileRegisterBtn {
  background: var(--primary-500);
  color: white;
  border: 1px solid var(--primary-500);
}

.mobileRegisterBtn:hover {
  background: var(--primary-600);
}

/* Overlay */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .desktopNav {
    display: none;
  }
  
  .mobileMenuBtn {
    display: flex;
  }
  
  .mobileNav {
    display: flex;
  }
  
  .navText {
    display: none;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-3);
    height: 60px;
  }
  
  .logoText {
    font-size: 1.25rem;
  }
  
  .userName {
    display: none;
  }
  
  .authActions {
    gap: var(--space-2);
  }
  
  .loginBtn,
  .registerBtn {
    padding: var(--space-2) var(--space-3);
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .logoIcon {
    font-size: 1.5rem;
  }
  
  .logoText {
    font-size: 1.125rem;
  }
  
  .profileDropdown {
    min-width: 260px;
    right: -20px;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .header {
    border-bottom: 2px solid #000;
  }
  
  .navLink,
  .profileBtn,
  .notificationBtn,
  .mobileMenuBtn {
    border: 1px solid rgba(255, 255, 255, 0.5);
  }
  
  .profileDropdown {
    border: 2px solid #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .navLink,
  .profileBtn,
  .notificationBtn,
  .mobileMenuBtn,
  .profileAction,
  .loginBtn,
  .registerBtn,
  .chevron {
    transition: none;
  }
}
