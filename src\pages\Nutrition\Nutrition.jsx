import { mealPlans } from '../../data/mockData';
import Button from '../../components/UI/Button/Button';
import Card from '../../components/UI/Card/Card';
import styles from './Nutrition.module.css';

const Nutrition = () => {
  return (
    <div className={styles.nutrition}>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>Nutrition Plans</h1>
          <p className={styles.subtitle}>
            Fuel your fitness journey with our expertly crafted meal plans
          </p>
        </div>

        <div className={styles.plansGrid}>
          {mealPlans.map(plan => (
            <Card key={plan.id} hover className={styles.planCard}>
              <div className={styles.planHeader}>
                <h3 className={styles.planName}>{plan.name}</h3>
                <div className={styles.planPrice}>${plan.price}</div>
              </div>
              
              <p className={styles.planDescription}>{plan.description}</p>
              
              <div className={styles.nutritionInfo}>
                <div className={styles.macros}>
                  <div className={styles.macro}>
                    <span className={styles.macroValue}>{plan.calories}</span>
                    <span className={styles.macroLabel}>Calories</span>
                  </div>
                  <div className={styles.macro}>
                    <span className={styles.macroValue}>{plan.protein}g</span>
                    <span className={styles.macroLabel}>Protein</span>
                  </div>
                  <div className={styles.macro}>
                    <span className={styles.macroValue}>{plan.carbs}g</span>
                    <span className={styles.macroLabel}>Carbs</span>
                  </div>
                  <div className={styles.macro}>
                    <span className={styles.macroValue}>{plan.fat}g</span>
                    <span className={styles.macroLabel}>Fat</span>
                  </div>
                </div>
              </div>

              <div className={styles.meals}>
                <h4 className={styles.mealsTitle}>Daily Meals</h4>
                {plan.meals.map((meal, index) => (
                  <div key={index} className={styles.meal}>
                    <div className={styles.mealHeader}>
                      <span className={styles.mealName}>{meal.name}</span>
                      <span className={styles.mealCalories}>{meal.calories} cal</span>
                    </div>
                    <div className={styles.mealItems}>
                      {meal.items.join(', ')}
                    </div>
                  </div>
                ))}
              </div>

              <div className={styles.planMeta}>
                <div className={styles.duration}>Duration: {plan.duration}</div>
                <div className={styles.tags}>
                  {plan.tags.map((tag, index) => (
                    <span key={index} className={styles.tag}>
                      {tag}
                    </span>
                  ))}
                </div>
              </div>

              <Button fullWidth>
                Get Plan
              </Button>
            </Card>
          ))}
        </div>

        <div className={styles.features}>
          <h2 className={styles.featuresTitle}>Why Our Nutrition Plans?</h2>
          <div className={styles.featuresGrid}>
            <div className={styles.feature}>
              <div className={styles.featureIcon}>👨‍⚕️</div>
              <h3 className={styles.featureTitle}>Expert Designed</h3>
              <p className={styles.featureText}>
                Created by certified nutritionists and dietitians
              </p>
            </div>
            <div className={styles.feature}>
              <div className={styles.featureIcon}>🎯</div>
              <h3 className={styles.featureTitle}>Goal Specific</h3>
              <p className={styles.featureText}>
                Tailored to your specific fitness and health goals
              </p>
            </div>
            <div className={styles.feature}>
              <div className={styles.featureIcon}>🛒</div>
              <h3 className={styles.featureTitle}>Shopping Lists</h3>
              <p className={styles.featureText}>
                Complete shopping lists and meal prep instructions
              </p>
            </div>
            <div className={styles.feature}>
              <div className={styles.featureIcon}>📱</div>
              <h3 className={styles.featureTitle}>Mobile Tracking</h3>
              <p className={styles.featureText}>
                Track your nutrition intake with our mobile app
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Nutrition;
