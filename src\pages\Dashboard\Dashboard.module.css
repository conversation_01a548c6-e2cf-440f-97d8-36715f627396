.dashboard {
  min-height: 100vh;
  background: var(--gray-50);
  padding: var(--space-8) 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

/* Welcome Section */
.welcomeSection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
  padding: var(--space-8);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
  color: white;
  border-radius: var(--radius-2xl);
}

.welcomeContent h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: var(--space-2);
}

.welcomeContent p {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0;
}

.welcomeActions {
  display: flex;
  gap: var(--space-4);
}

/* Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-6);
  margin-bottom: var(--space-12);
}

.statCard {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-6);
}

.statIcon {
  font-size: 2.5rem;
  line-height: 1;
}

.statValue {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  line-height: 1;
  margin-bottom: var(--space-1);
}

.statLabel {
  font-size: 0.875rem;
  color: var(--gray-600);
  font-weight: 500;
  margin-bottom: var(--space-1);
}

.statChange {
  font-size: 0.75rem;
  color: var(--success-600);
  font-weight: 500;
}

/* Sections */
.section {
  margin-bottom: var(--space-12);
}

.sectionTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-6);
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

/* Quick Actions */
.quickActionsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-6);
  margin-bottom: var(--space-12);
}

.actionCard {
  padding: 0;
  overflow: hidden;
}

.actionLink {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-6);
  text-decoration: none;
  color: inherit;
  transition: background var(--transition-fast);
}

.actionLink:hover {
  background: var(--gray-50);
}

.actionIcon {
  font-size: 2rem;
  line-height: 1;
}

.actionContent {
  flex: 1;
}

.actionTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-1);
}

.actionDescription {
  font-size: 0.875rem;
  color: var(--gray-600);
  margin: 0;
}

.actionArrow {
  font-size: 1.25rem;
  color: var(--gray-400);
}

/* Dashboard Grid */
.dashboardGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
  margin-bottom: var(--space-12);
}

/* Workouts */
.workoutsCard,
.classesCard,
.notificationsCard {
  padding: var(--space-6);
}

.workoutsList,
.classesList,
.notificationsList {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.workoutItem,
.classItem,
.notificationItem {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.workoutIcon,
.notificationIcon {
  font-size: 1.5rem;
  line-height: 1;
}

.workoutDetails,
.classInfo,
.notificationContent {
  flex: 1;
}

.workoutType,
.className,
.notificationTitle {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-1);
}

.workoutMeta,
.classMeta,
.notificationMessage {
  font-size: 0.875rem;
  color: var(--gray-600);
}

.workoutDate {
  font-size: 0.75rem;
  color: var(--gray-500);
  font-weight: 500;
}

.notificationTime {
  font-size: 0.75rem;
  color: var(--gray-500);
  margin-top: var(--space-1);
}

.classActions {
  flex-shrink: 0;
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: var(--space-8);
}

.emptyIcon {
  font-size: 3rem;
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.emptyText {
  color: var(--gray-600);
  margin-bottom: var(--space-6);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quickActionsGrid {
    grid-template-columns: 1fr;
  }
  
  .dashboardGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .welcomeSection {
    flex-direction: column;
    gap: var(--space-6);
    text-align: center;
  }
  
  .welcomeContent h1 {
    font-size: 2rem;
  }
  
  .welcomeActions {
    flex-direction: column;
    width: 100%;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .actionLink {
    padding: var(--space-4);
  }
  
  .workoutItem,
  .classItem,
  .notificationItem {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--space-3);
  }
  
  .dashboard {
    padding: var(--space-6) 0;
  }
  
  .welcomeSection {
    padding: var(--space-6);
  }
  
  .welcomeContent h1 {
    font-size: 1.75rem;
  }
}
