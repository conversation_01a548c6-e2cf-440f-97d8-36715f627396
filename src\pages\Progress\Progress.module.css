.progress {
  min-height: 100vh;
  background: var(--gray-50);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-6) var(--space-3);
}

/* Header Section */
.header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.125rem;
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Progress Grid */
.progressGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-6);
}

.goalsCard,
.workoutsCard,
.measurementsCard,
.statsCard {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  border: 1px solid var(--gray-200);
}

.cardTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-6);
}

/* Goals */
.goalsList {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.goal {
  padding: var(--space-4);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.goalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-3);
}

.goalTitle {
  font-size: 1rem;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.goalProgress {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary-600);
}

.progressBar {
  width: 100%;
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--space-3);
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-500) 0%, var(--secondary-500) 100%);
  border-radius: var(--radius-full);
  transition: width var(--transition-normal);
}

.goalDetails {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-2);
}

.current,
.target {
  font-size: 0.875rem;
  color: var(--gray-600);
}

.deadline {
  font-size: 0.75rem;
  color: var(--gray-500);
}

/* Workouts */
.workoutsList {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.workout {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.workoutIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.workoutDetails {
  flex: 1;
}

.workoutType {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-1);
}

.workoutMeta {
  font-size: 0.75rem;
  color: var(--gray-600);
}

.workoutDate {
  font-size: 0.75rem;
  color: var(--gray-500);
  font-weight: 500;
}

/* Measurements */
.measurementsList {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.measurement {
  padding: var(--space-4);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.measurementDate {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-3);
}

.measurementData {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.dataItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  font-size: 0.75rem;
  color: var(--gray-500);
  font-weight: 500;
}

.value {
  font-size: 0.875rem;
  color: var(--gray-900);
  font-weight: 600;
}

/* Statistics */
.statsList {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-4);
}

.stat {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.statIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.statContent {
  flex: 1;
}

.statValue {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-1);
}

.statLabel {
  font-size: 0.75rem;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}



/* Responsive Design */
@media (min-width: 768px) {
  .container {
    padding: var(--space-8) var(--space-4);
  }

  .title {
    font-size: 2.5rem;
  }

  .progressGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .statsList {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: var(--space-12) var(--space-6);
  }

  .title {
    font-size: 3rem;
  }

  .progressGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .statsList {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 767px) {
  .progressGrid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .workout {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .measurementData {
    gap: var(--space-1);
  }

  .statsList {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .stat {
    flex-direction: column;
    text-align: center;
    gap: var(--space-2);
  }
}

@media (max-width: 480px) {
  .goalsCard,
  .workoutsCard,
  .measurementsCard,
  .statsCard {
    padding: var(--space-4);
  }

  .goal,
  .workout,
  .measurement,
  .stat {
    padding: var(--space-3);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .goalsCard,
  .workoutsCard,
  .measurementsCard,
  .statsCard {
    border: 2px solid #000;
  }

  .title {
    -webkit-text-fill-color: #000;
    color: #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .progressFill {
    transition: none;
  }
}
