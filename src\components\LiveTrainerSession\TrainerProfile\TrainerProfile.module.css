.trainerProfile {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
}

.profileHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.avatarContainer {
  position: relative;
  flex-shrink: 0;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #e5e7eb;
  transition: border-color 0.2s ease;
}

.statusIndicator {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 3px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statusIndicator.online {
  background: #10b981;
}

.statusIndicator.offline {
  background: #6b7280;
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: white;
}

.statusIndicator.online .statusDot {
  animation: pulse 2s infinite;
}

.profileInfo {
  flex: 1;
  min-width: 0;
}

.trainerName {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.credentials {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.credential {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.profileDetails {
  margin-bottom: 2rem;
}

.rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.stars {
  display: flex;
  gap: 0.125rem;
}

.star {
  width: 1rem;
  height: 1rem;
  color: #fbbf24;
}

.ratingText {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.currentFocus {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #0ea5e9;
}

.focusLabel {
  font-size: 0.875rem;
  font-weight: 600;
  color: #0369a1;
}

.focusValue {
  font-size: 0.875rem;
  color: #0c4a6e;
  font-weight: 500;
}

.specialties {
  margin-bottom: 1rem;
}

.specialtiesLabel {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.specialtyTags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.specialtyTag {
  background: #f3f4f6;
  color: #374151;
  padding: 0.375rem 0.75rem;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid #d1d5db;
}

.actions {
  display: flex;
  gap: 0.75rem;
}

.messageBtn,
.callBtn,
.videoBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  position: relative;
  min-height: 44px; /* Touch-friendly minimum */
}

.messageBtn {
  flex: 1;
  background: #667eea;
  color: white;
}

.messageBtn:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.callBtn,
.videoBtn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  flex-shrink: 0;
}

.callBtn:hover,
.videoBtn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.icon {
  width: 1.125rem;
  height: 1.125rem;
}

.unreadBadge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ef4444;
  color: white;
  font-size: 0.625rem;
  font-weight: 700;
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  line-height: 1.2;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .trainerProfile {
    padding: 1.5rem;
  }
  
  .profileHeader {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .trainerName {
    font-size: 1.25rem;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .callBtn,
  .videoBtn {
    flex: 1;
  }
  
  .currentFocus {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

@media (max-width: 480px) {
  .avatar {
    width: 60px;
    height: 60px;
  }
  
  .statusIndicator {
    width: 16px;
    height: 16px;
    bottom: 2px;
    right: 2px;
  }
  
  .statusDot {
    width: 6px;
    height: 6px;
  }
  
  .credentials {
    justify-content: center;
  }
  
  .specialtyTags {
    justify-content: center;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .messageBtn,
  .callBtn,
  .videoBtn,
  .statusDot {
    transition: none;
    animation: none;
  }
}

/* Focus styles for accessibility */
.messageBtn:focus,
.callBtn:focus,
.videoBtn:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.messageBtn:focus:not(:focus-visible),
.callBtn:focus:not(:focus-visible),
.videoBtn:focus:not(:focus-visible) {
  outline: none;
}
