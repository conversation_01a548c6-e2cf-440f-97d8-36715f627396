import { useMemo } from 'react';
import styles from './ProgressRing.module.css';

/**
 * Circular progress ring component
 * @param {Object} props
 * @param {number} props.progress - Progress percentage (0-100)
 * @param {number} props.size - Ring size in pixels
 * @param {number} props.strokeWidth - Ring stroke width
 * @param {string} props.color - Ring color
 * @param {string} props.backgroundColor - Background ring color
 * @param {boolean} props.showPercentage - Whether to show percentage text
 * @param {React.ReactNode} props.children - Content to display in center
 * @param {string} props.className - Additional CSS classes
 */
const ProgressRing = ({
  progress = 0,
  size = 120,
  strokeWidth = 8,
  color = '#667eea',
  backgroundColor = '#e5e7eb',
  showPercentage = false,
  children,
  className = ''
}) => {
  const normalizedProgress = Math.min(100, Math.max(0, progress));
  
  const { radius, circumference, strokeDasharray, strokeDashoffset } = useMemo(() => {
    const r = (size - strokeWidth) / 2;
    const c = 2 * Math.PI * r;
    const offset = c - (normalizedProgress / 100) * c;
    
    return {
      radius: r,
      circumference: c,
      strokeDasharray: c,
      strokeDashoffset: offset
    };
  }, [size, strokeWidth, normalizedProgress]);
  
  const center = size / 2;
  
  return (
    <div 
      className={`${styles.progressRing} ${className}`}
      style={{ width: size, height: size }}
    >
      <svg
        width={size}
        height={size}
        className={styles.svg}
      >
        {/* Background circle */}
        <circle
          cx={center}
          cy={center}
          r={radius}
          fill="none"
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          className={styles.backgroundCircle}
        />
        
        {/* Progress circle */}
        <circle
          cx={center}
          cy={center}
          r={radius}
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={styles.progressCircle}
          style={{
            transition: 'stroke-dashoffset 0.3s ease-in-out'
          }}
        />
      </svg>
      
      {/* Center content */}
      <div className={styles.content}>
        {children || (showPercentage && (
          <span className={styles.percentage}>
            {Math.round(normalizedProgress)}%
          </span>
        ))}
      </div>
    </div>
  );
};

export default ProgressRing;
