.authPage {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
  padding: var(--space-4);
}

.authContainer {
  width: 100%;
  max-width: 500px;
}

.authCard {
  padding: var(--space-8);
  box-shadow: var(--shadow-xl);
}

.authHeader {
  text-align: center;
  margin-bottom: var(--space-8);
}

.authTitle {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-2);
}

.authSubtitle {
  color: var(--gray-600);
  font-size: 1rem;
  line-height: 1.5;
  margin: 0;
}

.authForm {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.label {
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.875rem;
}

.input {
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
  background: white;
}

.input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input::placeholder {
  color: var(--gray-400);
}

.inputError {
  border-color: var(--error-500);
}

.inputError:focus {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.errorMessage {
  color: var(--error-500);
  font-size: 0.75rem;
  font-weight: 500;
}

.errorAlert {
  background: var(--error-50);
  border: 1px solid var(--error-200);
  color: var(--error-700);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
}

.formOptions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: var(--space-2) 0;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  font-size: 0.875rem;
}

.checkbox {
  width: 1rem;
  height: 1rem;
  accent-color: var(--primary-500);
}

.checkboxText {
  color: var(--gray-600);
  line-height: 1.4;
}

.forgotLink {
  color: var(--primary-500);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: color var(--transition-fast);
}

.forgotLink:hover {
  color: var(--primary-600);
}

.authDivider {
  position: relative;
  text-align: center;
  margin: var(--space-6) 0;
}

.authDivider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gray-300);
}

.dividerText {
  background: white;
  color: var(--gray-500);
  padding: 0 var(--space-4);
  font-size: 0.875rem;
  position: relative;
}

.demoButtons {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.authFooter {
  text-align: center;
  margin-top: var(--space-6);
  padding-top: var(--space-6);
  border-top: 1px solid var(--gray-200);
}

.authFooterText {
  color: var(--gray-600);
  font-size: 0.875rem;
  margin: 0;
}

.authLink {
  color: var(--primary-500);
  text-decoration: none;
  font-weight: 600;
  transition: color var(--transition-fast);
}

.authLink:hover {
  color: var(--primary-600);
}

/* Responsive Design */
@media (max-width: 768px) {
  .authPage {
    padding: var(--space-3);
    align-items: flex-start;
    padding-top: var(--space-8);
  }
  
  .authContainer {
    max-width: 100%;
  }
  
  .authCard {
    padding: var(--space-6);
  }
  
  .authTitle {
    font-size: 1.75rem;
  }
  
  .formRow {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .formOptions {
    flex-direction: column;
    gap: var(--space-3);
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .authCard {
    padding: var(--space-4);
  }
  
  .authTitle {
    font-size: 1.5rem;
  }
  
  .authSubtitle {
    font-size: 0.875rem;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .input {
    border: 2px solid #000;
  }
  
  .input:focus {
    border-color: #000;
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
  }
  
  .inputError {
    border-color: var(--error-500);
  }
  
  .authLink,
  .forgotLink {
    text-decoration: underline;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .input,
  .authLink,
  .forgotLink {
    transition: none;
  }
}

/* Touch Devices */
@media (pointer: coarse) {
  .input {
    min-height: 48px;
    padding: var(--space-4);
  }
  
  .checkbox {
    width: 1.25rem;
    height: 1.25rem;
  }
  
  .checkboxLabel {
    min-height: 44px;
    align-items: center;
  }
}
