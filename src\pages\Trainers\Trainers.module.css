.trainers {
  min-height: 100vh;
  background: var(--gray-50);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-6) var(--space-3);
}

/* Header Section */
.header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.125rem;
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Filters */
.filters {
  margin-bottom: var(--space-8);
}

.searchBox {
  margin-bottom: var(--space-4);
}

.searchInput {
  width: 100%;
  max-width: 400px;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  transition: border-color var(--transition-fast);
}

.searchInput:focus {
  border-color: var(--primary-500);
  outline: none;
}

.specialtyFilters {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
  justify-content: center;
}

.filterBtn {
  padding: var(--space-2) var(--space-4);
  border: 2px solid var(--gray-300);
  background: white;
  color: var(--gray-700);
  border-radius: var(--radius-full);
  font-weight: 500;
  transition: all var(--transition-fast);
  cursor: pointer;
  min-height: 44px;
  font-size: 0.875rem;
}

.filterBtn:hover {
  border-color: var(--primary-500);
  color: var(--primary-600);
  transform: translateY(-1px);
}

.filterBtn.active {
  background: var(--primary-500);
  border-color: var(--primary-500);
  color: white;
}

/* Trainers Grid */
.trainersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--space-6);
}

.trainerCard {
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
}

.trainerCard:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.trainerHeader {
  position: relative;
  padding: var(--space-4);
  text-align: center;
}

.trainerAvatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid white;
  box-shadow: var(--shadow-md);
}

.statusBadge {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statusBadge.online {
  background: var(--success-100);
  color: var(--success-700);
}

.statusBadge.offline {
  background: var(--gray-100);
  color: var(--gray-600);
}

.trainerInfo {
  padding: var(--space-6);
  padding-top: 0;
}

.trainerName {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-2);
  text-align: center;
}

.trainerBio {
  font-size: 0.875rem;
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: var(--space-4);
  text-align: center;
}

.trainerMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding: var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.rating {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.stars {
  font-size: 0.875rem;
  color: var(--warning-500);
  font-weight: 600;
}

.reviews {
  font-size: 0.75rem;
  color: var(--gray-500);
}

.experience {
  font-size: 0.75rem;
  color: var(--gray-600);
  font-weight: 500;
}

.credentials {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.credential {
  padding: var(--space-1) var(--space-2);
  background: var(--secondary-100);
  color: var(--secondary-700);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.specialties {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.specialty {
  padding: var(--space-1) var(--space-3);
  background: var(--primary-100);
  color: var(--primary-700);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
}

.moreSpecialties {
  padding: var(--space-1) var(--space-3);
  background: var(--gray-100);
  color: var(--gray-600);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
}

.pricing {
  text-align: center;
  margin-bottom: var(--space-6);
  padding: var(--space-3);
  background: var(--success-50);
  border-radius: var(--radius-md);
}

.rate {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--success-700);
}

.trainerActions {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: var(--space-12);
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.emptyTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-3);
}

.emptyText {
  font-size: 0.875rem;
  color: var(--gray-600);
  margin-bottom: var(--space-6);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive Design */
@media (min-width: 768px) {
  .container {
    padding: var(--space-8) var(--space-4);
  }

  .title {
    font-size: 2.5rem;
  }

  .trainersGrid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--space-8);
  }

  .specialtyFilters {
    justify-content: center;
  }

  .trainerActions {
    flex-direction: row;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: var(--space-12) var(--space-6);
  }

  .title {
    font-size: 3rem;
  }

  .trainersGrid {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  }
}

@media (max-width: 767px) {
  .trainersGrid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .specialtyFilters {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: var(--space-2);
  }

  .filterBtn {
    flex-shrink: 0;
    padding: var(--space-2) var(--space-3);
    font-size: 0.8rem;
  }

  .trainerMeta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .trainerAvatar {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .trainerInfo {
    padding: var(--space-4);
  }

  .trainerName {
    font-size: 1.125rem;
  }

  .trainerHeader {
    padding: var(--space-3);
  }

  .trainerAvatar {
    width: 80px;
    height: 80px;
  }

  .emptyState {
    padding: var(--space-8);
  }

  .emptyIcon {
    font-size: 3rem;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .trainerCard,
  .filterBtn,
  .searchInput {
    border: 2px solid #000;
  }

  .title {
    -webkit-text-fill-color: #000;
    color: #000;
  }

  .specialty,
  .credential,
  .statusBadge {
    border: 1px solid #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .trainerCard,
  .filterBtn {
    transition: none;
    transform: none;
  }

  .trainerCard:hover,
  .filterBtn:hover {
    transform: none;
  }
}
