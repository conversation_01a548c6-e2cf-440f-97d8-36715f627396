.card {
  background: white;
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

/* Variants */
.default {
  /* Default styling already applied above */
}

.elevated {
  box-shadow: var(--shadow-md);
}

.outlined {
  border: 2px solid var(--gray-300);
  box-shadow: none;
}

.gradient {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
  color: white;
  border: none;
}

/* Padding variants */
.padding-none {
  padding: 0;
}

.padding-small {
  padding: var(--space-4);
}

.padding-medium {
  padding: var(--space-6);
}

.padding-large {
  padding: var(--space-8);
}

/* Hover effect */
.hover:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-200);
}

.hover.gradient:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid #000;
  }
  
  .outlined {
    border: 3px solid #000;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .card {
    transition: none;
  }
  
  .hover:hover {
    transform: none;
  }
}
