import { useState } from 'react';
import { trainers } from '../../data/mockData';
import Button from '../../components/UI/Button/Button';
import Card from '../../components/UI/Card/Card';
import styles from './Trainers.module.css';

const Trainers = () => {
  const [selectedSpecialty, setSelectedSpecialty] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const specialties = ['all', 'Strength Training', 'HIIT', 'Yoga', 'Pilates', 'Powerlifting'];

  const filteredTrainers = trainers.filter(trainer => {
    const matchesSpecialty = selectedSpecialty === 'all' || 
      trainer.specialties.includes(selectedSpecialty);
    const matchesSearch = trainer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      trainer.specialties.some(s => s.toLowerCase().includes(searchTerm.toLowerCase()));
    
    return matchesSpecialty && matchesSearch;
  });

  return (
    <div className={styles.trainers}>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>Our Expert Trainers</h1>
          <p className={styles.subtitle}>
            Find the perfect trainer to guide your fitness journey
          </p>
        </div>

        <div className={styles.filters}>
          <div className={styles.searchBox}>
            <input
              type="text"
              placeholder="Search trainers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>
          
          <div className={styles.specialtyFilters}>
            {specialties.map(specialty => (
              <button
                key={specialty}
                onClick={() => setSelectedSpecialty(specialty)}
                className={`${styles.filterBtn} ${
                  selectedSpecialty === specialty ? styles.active : ''
                }`}
              >
                {specialty === 'all' ? 'All Specialties' : specialty}
              </button>
            ))}
          </div>
        </div>

        <div className={styles.trainersGrid}>
          {filteredTrainers.map(trainer => (
            <Card key={trainer.id} hover className={styles.trainerCard}>
              <div className={styles.trainerHeader}>
                <img
                  src={trainer.avatar}
                  alt={trainer.name}
                  className={styles.trainerAvatar}
                  onError={(e) => {
                    e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(trainer.name)}&background=667eea&color=fff&size=200`;
                  }}
                />
                <div className={`${styles.statusBadge} ${trainer.isOnline ? styles.online : styles.offline}`}>
                  {trainer.isOnline ? 'Online' : 'Offline'}
                </div>
              </div>
              
              <div className={styles.trainerInfo}>
                <h3 className={styles.trainerName}>{trainer.name}</h3>
                <p className={styles.trainerBio}>{trainer.bio}</p>
                
                <div className={styles.trainerMeta}>
                  <div className={styles.rating}>
                    <span className={styles.stars}>⭐ {trainer.rating}</span>
                    <span className={styles.reviews}>({trainer.reviewCount} reviews)</span>
                  </div>
                  <div className={styles.experience}>
                    {trainer.experience} experience
                  </div>
                </div>

                <div className={styles.credentials}>
                  {trainer.credentials.map((cert, index) => (
                    <span key={index} className={styles.credential}>
                      {cert}
                    </span>
                  ))}
                </div>

                <div className={styles.specialties}>
                  {trainer.specialties.slice(0, 3).map((specialty, index) => (
                    <span key={index} className={styles.specialty}>
                      {specialty}
                    </span>
                  ))}
                  {trainer.specialties.length > 3 && (
                    <span className={styles.moreSpecialties}>
                      +{trainer.specialties.length - 3} more
                    </span>
                  )}
                </div>

                <div className={styles.pricing}>
                  <span className={styles.rate}>${trainer.hourlyRate}/hour</span>
                </div>

                <div className={styles.trainerActions}>
                  <Button fullWidth>
                    Book Session
                  </Button>
                  <Button variant="outline" fullWidth>
                    View Profile
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {filteredTrainers.length === 0 && (
          <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>🔍</div>
            <h3 className={styles.emptyTitle}>No trainers found</h3>
            <p className={styles.emptyText}>
              Try adjusting your search or filter criteria
            </p>
            <Button onClick={() => {
              setSearchTerm('');
              setSelectedSpecialty('all');
            }}>
              Clear Filters
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Trainers;
