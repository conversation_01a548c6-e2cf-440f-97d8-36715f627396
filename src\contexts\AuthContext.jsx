import { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { users } from '../data/mockData';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check for existing session on mount
  useEffect(() => {
    const checkExistingSession = () => {
      try {
        const savedUser = localStorage.getItem('gym_user');
        if (savedUser) {
          const userData = JSON.parse(savedUser);
          setUser(userData);
        }
      } catch (error) {
        console.error('Error checking existing session:', error);
        localStorage.removeItem('gym_user');
      } finally {
        setIsLoading(false);
      }
    };

    checkExistingSession();
  }, []);

  // Login function
  const login = useCallback(async (email, password) => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Find user in mock data
      const foundUser = users.find(u => u.email === email && u.password === password);
      
      if (!foundUser) {
        throw new Error('Invalid email or password');
      }

      // Remove password from user object before storing
      const { password: _, ...userWithoutPassword } = foundUser;
      
      setUser(userWithoutPassword);
      localStorage.setItem('gym_user', JSON.stringify(userWithoutPassword));
      
      return { success: true, user: userWithoutPassword };
    } catch (error) {
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Register function
  const register = useCallback(async (userData) => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check if user already exists
      const existingUser = users.find(u => u.email === userData.email);
      if (existingUser) {
        throw new Error('User with this email already exists');
      }

      // Create new user
      const newUser = {
        id: `user-${Date.now()}`,
        ...userData,
        role: 'member',
        membershipType: 'basic',
        joinDate: new Date().toISOString().split('T')[0],
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.firstName + ' ' + userData.lastName)}&background=667eea&color=fff&size=150`,
        preferences: {
          notifications: true,
          newsletter: true,
          workoutReminders: true
        },
        stats: {
          totalWorkouts: 0,
          totalHours: 0,
          currentStreak: 0,
          longestStreak: 0
        }
      };

      // Remove password from user object before storing
      const { password: _, ...userWithoutPassword } = newUser;
      
      // Add to mock users array (in real app, this would be an API call)
      users.push(newUser);
      
      setUser(userWithoutPassword);
      localStorage.setItem('gym_user', JSON.stringify(userWithoutPassword));
      
      return { success: true, user: userWithoutPassword };
    } catch (error) {
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Logout function
  const logout = useCallback(() => {
    setUser(null);
    setError(null);
    localStorage.removeItem('gym_user');
  }, []);

  // Update user profile
  const updateProfile = useCallback(async (updates) => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const updatedUser = { ...user, ...updates };
      setUser(updatedUser);
      localStorage.setItem('gym_user', JSON.stringify(updatedUser));
      
      return { success: true, user: updatedUser };
    } catch (error) {
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Reset password function
  const resetPassword = useCallback(async (email) => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check if user exists
      const foundUser = users.find(u => u.email === email);
      if (!foundUser) {
        throw new Error('No user found with this email address');
      }

      // In a real app, this would send a reset email
      console.log(`Password reset email sent to ${email}`);
      
      return { success: true, message: 'Password reset email sent' };
    } catch (error) {
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Check if user has permission
  const hasPermission = useCallback((permission) => {
    if (!user) return false;
    
    switch (permission) {
      case 'admin':
        return user.role === 'admin';
      case 'trainer':
        return user.role === 'trainer' || user.role === 'admin';
      case 'member':
        return user.role === 'member' || user.role === 'trainer' || user.role === 'admin';
      case 'premium':
        return user.membershipType === 'premium' || user.membershipType === 'elite' || user.role === 'admin';
      case 'elite':
        return user.membershipType === 'elite' || user.role === 'admin';
      default:
        return false;
    }
  }, [user]);

  // Get user display name
  const getDisplayName = useCallback(() => {
    if (!user) return 'Guest';
    return `${user.firstName} ${user.lastName}`;
  }, [user]);

  // Check if user is authenticated
  const isAuthenticated = !!user;

  const value = {
    // State
    user,
    isLoading,
    error,
    isAuthenticated,
    
    // Actions
    login,
    register,
    logout,
    updateProfile,
    resetPassword,
    
    // Utilities
    hasPermission,
    getDisplayName,
    
    // Clear error
    clearError: () => setError(null)
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
