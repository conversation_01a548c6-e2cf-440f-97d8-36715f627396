// Mock data for the gym management system

// Users data
export const users = [
  {
    id: 'user-001',
    email: '<EMAIL>',
    password: 'password123', // In real app, this would be hashed
    firstName: '<PERSON>',
    lastName: 'Doe',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    role: 'member',
    membershipType: 'premium',
    joinDate: '2023-01-15',
    phone: '+****************',
    emergencyContact: {
      name: '<PERSON>',
      phone: '+****************',
      relationship: 'Spouse'
    },
    preferences: {
      notifications: true,
      newsletter: true,
      workoutReminders: true
    },
    stats: {
      totalWorkouts: 156,
      totalHours: 234,
      currentStreak: 12,
      longestStreak: 28
    }
  },
  {
    id: 'admin-001',
    email: '<EMAIL>',
    password: 'admin123',
    firstName: 'Sarah',
    lastName: 'Admin',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    role: 'admin',
    joinDate: '2022-01-01'
  }
];

// Trainers data
export const trainers = [
  {
    id: 'trainer-001',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=150&h=150&fit=crop&crop=face',
    credentials: ['NASM-CPT', 'ACSM-EP', 'Yoga Alliance RYT-200'],
    rating: 4.9,
    reviewCount: 127,
    specialties: ['Strength Training', 'HIIT', 'Functional Fitness', 'Yoga'],
    bio: 'Sarah has been helping clients achieve their fitness goals for over 8 years. She specializes in strength training and functional movement patterns.',
    experience: '8+ years',
    isOnline: true,
    currentFocus: 'Upper Body Strength',
    hourlyRate: 85,
    availability: {
      monday: ['09:00', '10:00', '11:00', '14:00', '15:00', '16:00'],
      tuesday: ['08:00', '09:00', '10:00', '15:00', '16:00', '17:00'],
      wednesday: ['09:00', '10:00', '11:00', '14:00', '15:00', '16:00'],
      thursday: ['08:00', '09:00', '10:00', '15:00', '16:00', '17:00'],
      friday: ['09:00', '10:00', '11:00', '14:00', '15:00'],
      saturday: ['08:00', '09:00', '10:00', '11:00'],
      sunday: ['10:00', '11:00', '12:00']
    },
    languages: ['English', 'Spanish'],
    achievements: [
      'NASM Trainer of the Year 2023',
      'Certified Nutrition Coach',
      'Former Competitive Athlete'
    ]
  },
  {
    id: 'trainer-002',
    name: 'Mike Rodriguez',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=150&h=150&fit=crop&crop=face',
    credentials: ['ACSM-CPT', 'CSCS', 'FMS Level 2'],
    rating: 4.8,
    reviewCount: 98,
    specialties: ['Powerlifting', 'Olympic Lifting', 'Sports Performance', 'Injury Prevention'],
    bio: 'Mike is a former collegiate athlete who brings his competitive spirit to every training session. He specializes in strength and power development.',
    experience: '6+ years',
    isOnline: false,
    currentFocus: 'Powerlifting Technique',
    hourlyRate: 90,
    availability: {
      monday: ['06:00', '07:00', '08:00', '17:00', '18:00', '19:00'],
      tuesday: ['06:00', '07:00', '08:00', '17:00', '18:00', '19:00'],
      wednesday: ['06:00', '07:00', '08:00', '17:00', '18:00', '19:00'],
      thursday: ['06:00', '07:00', '08:00', '17:00', '18:00', '19:00'],
      friday: ['06:00', '07:00', '08:00', '17:00', '18:00'],
      saturday: ['08:00', '09:00', '10:00', '11:00', '12:00'],
      sunday: []
    },
    languages: ['English'],
    achievements: [
      'USAPL Certified Referee',
      'Former D1 Football Player',
      'Certified Strength Coach'
    ]
  },
  {
    id: 'trainer-003',
    name: 'Emily Chen',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    credentials: ['ACE-CPT', 'Pilates Certification', 'Barre Instructor'],
    rating: 4.9,
    reviewCount: 156,
    specialties: ['Pilates', 'Barre', 'Flexibility', 'Core Training', 'Posture Correction'],
    bio: 'Emily focuses on mind-body connection and helps clients build strength through controlled movements and proper alignment.',
    experience: '5+ years',
    isOnline: true,
    currentFocus: 'Core Stability',
    hourlyRate: 80,
    availability: {
      monday: ['10:00', '11:00', '12:00', '13:00', '14:00'],
      tuesday: ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00'],
      wednesday: ['10:00', '11:00', '12:00', '13:00', '14:00'],
      thursday: ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00'],
      friday: ['10:00', '11:00', '12:00', '13:00'],
      saturday: ['09:00', '10:00', '11:00'],
      sunday: ['10:00', '11:00', '12:00', '13:00']
    },
    languages: ['English', 'Mandarin'],
    achievements: [
      'Certified Pilates Instructor',
      'Former Professional Dancer',
      'Posture Specialist'
    ]
  }
];

// Classes data
export const classes = [
  {
    id: 'class-001',
    name: 'HIIT Bootcamp',
    instructor: 'trainer-001',
    description: 'High-intensity interval training that combines cardio and strength exercises for maximum calorie burn.',
    duration: 45,
    maxCapacity: 20,
    currentEnrollment: 15,
    difficulty: 'Intermediate',
    equipment: ['Dumbbells', 'Kettlebells', 'Resistance Bands', 'Mats'],
    schedule: [
      { day: 'Monday', time: '06:00', date: '2024-08-05' },
      { day: 'Wednesday', time: '06:00', date: '2024-08-07' },
      { day: 'Friday', time: '06:00', date: '2024-08-09' },
      { day: 'Monday', time: '18:00', date: '2024-08-05' },
      { day: 'Wednesday', time: '18:00', date: '2024-08-07' }
    ],
    price: 25,
    tags: ['Cardio', 'Strength', 'Fat Loss', 'High Intensity']
  },
  {
    id: 'class-002',
    name: 'Yoga Flow',
    instructor: 'trainer-001',
    description: 'A dynamic yoga practice that links movement with breath to build strength, flexibility, and mindfulness.',
    duration: 60,
    maxCapacity: 25,
    currentEnrollment: 18,
    difficulty: 'Beginner',
    equipment: ['Yoga Mats', 'Blocks', 'Straps'],
    schedule: [
      { day: 'Tuesday', time: '07:00', date: '2024-08-06' },
      { day: 'Thursday', time: '07:00', date: '2024-08-08' },
      { day: 'Saturday', time: '09:00', date: '2024-08-10' },
      { day: 'Sunday', time: '10:00', date: '2024-08-11' }
    ],
    price: 20,
    tags: ['Flexibility', 'Mindfulness', 'Stress Relief', 'Balance']
  },
  {
    id: 'class-003',
    name: 'Strength & Power',
    instructor: 'trainer-002',
    description: 'Focus on compound movements and progressive overload to build maximum strength and power.',
    duration: 60,
    maxCapacity: 12,
    currentEnrollment: 10,
    difficulty: 'Advanced',
    equipment: ['Barbells', 'Plates', 'Power Rack', 'Bench'],
    schedule: [
      { day: 'Monday', time: '17:00', date: '2024-08-05' },
      { day: 'Wednesday', time: '17:00', date: '2024-08-07' },
      { day: 'Friday', time: '17:00', date: '2024-08-09' }
    ],
    price: 30,
    tags: ['Strength', 'Powerlifting', 'Muscle Building', 'Advanced']
  },
  {
    id: 'class-004',
    name: 'Pilates Core',
    instructor: 'trainer-003',
    description: 'Strengthen your core and improve posture with controlled Pilates movements.',
    duration: 50,
    maxCapacity: 15,
    currentEnrollment: 12,
    difficulty: 'Intermediate',
    equipment: ['Pilates Mats', 'Resistance Bands', 'Pilates Balls'],
    schedule: [
      { day: 'Tuesday', time: '12:00', date: '2024-08-06' },
      { day: 'Thursday', time: '12:00', date: '2024-08-08' },
      { day: 'Saturday', time: '10:00', date: '2024-08-10' }
    ],
    price: 22,
    tags: ['Core', 'Posture', 'Flexibility', 'Low Impact']
  }
];

// Membership plans
export const membershipPlans = [
  {
    id: 'basic',
    name: 'Basic',
    price: 29.99,
    billing: 'monthly',
    features: [
      'Access to gym equipment',
      'Locker room access',
      'Basic fitness assessment',
      'Mobile app access'
    ],
    limitations: [
      'No group classes',
      'No personal training',
      'Limited peak hours access'
    ],
    popular: false
  },
  {
    id: 'premium',
    name: 'Premium',
    price: 59.99,
    billing: 'monthly',
    features: [
      'All Basic features',
      'Unlimited group classes',
      '2 personal training sessions/month',
      '24/7 gym access',
      'Nutrition consultation',
      'Guest passes (2/month)'
    ],
    limitations: [],
    popular: true
  },
  {
    id: 'elite',
    name: 'Elite',
    price: 99.99,
    billing: 'monthly',
    features: [
      'All Premium features',
      'Unlimited personal training',
      'Meal planning service',
      'Recovery services (massage, sauna)',
      'Priority class booking',
      'Unlimited guest passes'
    ],
    limitations: [],
    popular: false
  }
];

// Equipment data
export const equipment = [
  {
    id: 'eq-001',
    name: 'Treadmill',
    category: 'Cardio',
    brand: 'Life Fitness',
    model: 'T5 Track+',
    status: 'available',
    location: 'Cardio Zone A',
    lastMaintenance: '2024-07-15',
    nextMaintenance: '2024-08-15',
    features: ['Heart Rate Monitor', 'Incline Control', 'Pre-set Programs'],
    image: 'https://images.unsplash.com/photo-1571902943202-507ec2618e8f?w=300&h=200&fit=crop'
  },
  {
    id: 'eq-002',
    name: 'Power Rack',
    category: 'Strength',
    brand: 'Rogue Fitness',
    model: 'R-3 Power Rack',
    status: 'in-use',
    location: 'Free Weight Area',
    lastMaintenance: '2024-07-20',
    nextMaintenance: '2024-08-20',
    features: ['Pull-up Bar', 'Safety Bars', 'Weight Storage'],
    image: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=300&h=200&fit=crop'
  },
  {
    id: 'eq-003',
    name: 'Elliptical',
    category: 'Cardio',
    brand: 'Precor',
    model: 'EFX 885',
    status: 'maintenance',
    location: 'Cardio Zone B',
    lastMaintenance: '2024-08-01',
    nextMaintenance: '2024-09-01',
    features: ['Cross Ramp', 'Moving Handles', 'Heart Rate Monitor'],
    image: 'https://images.unsplash.com/photo-1571902943202-507ec2618e8f?w=300&h=200&fit=crop'
  }
];

// Nutrition data
export const mealPlans = [
  {
    id: 'plan-001',
    name: 'Muscle Building',
    description: 'High protein meal plan designed to support muscle growth and recovery.',
    calories: 2800,
    protein: 180,
    carbs: 280,
    fat: 93,
    duration: '4 weeks',
    price: 149.99,
    meals: [
      {
        name: 'Breakfast',
        calories: 650,
        items: ['Oatmeal with berries', 'Greek yogurt', 'Protein shake']
      },
      {
        name: 'Lunch',
        calories: 750,
        items: ['Grilled chicken breast', 'Brown rice', 'Mixed vegetables']
      },
      {
        name: 'Dinner',
        calories: 800,
        items: ['Salmon fillet', 'Sweet potato', 'Asparagus']
      },
      {
        name: 'Snacks',
        calories: 600,
        items: ['Almonds', 'Apple with peanut butter', 'Protein bar']
      }
    ],
    tags: ['High Protein', 'Muscle Building', 'Performance']
  },
  {
    id: 'plan-002',
    name: 'Weight Loss',
    description: 'Balanced, calorie-controlled meal plan for sustainable weight loss.',
    calories: 1800,
    protein: 120,
    carbs: 180,
    fat: 60,
    duration: '6 weeks',
    price: 129.99,
    meals: [
      {
        name: 'Breakfast',
        calories: 400,
        items: ['Egg white omelet', 'Whole grain toast', 'Fresh fruit']
      },
      {
        name: 'Lunch',
        calories: 500,
        items: ['Grilled chicken salad', 'Quinoa', 'Olive oil dressing']
      },
      {
        name: 'Dinner',
        calories: 550,
        items: ['Lean fish', 'Roasted vegetables', 'Small portion brown rice']
      },
      {
        name: 'Snacks',
        calories: 350,
        items: ['Greek yogurt', 'Vegetables with hummus', 'Herbal tea']
      }
    ],
    tags: ['Weight Loss', 'Low Calorie', 'Balanced']
  }
];

// Progress tracking data
export const progressData = {
  workouts: [
    { date: '2024-07-01', duration: 45, calories: 320, type: 'Strength' },
    { date: '2024-07-03', duration: 60, calories: 450, type: 'Cardio' },
    { date: '2024-07-05', duration: 50, calories: 380, type: 'HIIT' },
    { date: '2024-07-08', duration: 40, calories: 290, type: 'Strength' },
    { date: '2024-07-10', duration: 55, calories: 410, type: 'Yoga' }
  ],
  measurements: [
    { date: '2024-07-01', weight: 180, bodyFat: 15.2, muscle: 145 },
    { date: '2024-07-15', weight: 178, bodyFat: 14.8, muscle: 146 },
    { date: '2024-08-01', weight: 176, bodyFat: 14.5, muscle: 147 }
  ],
  goals: [
    {
      id: 'goal-001',
      title: 'Lose 10 pounds',
      target: 170,
      current: 176,
      unit: 'lbs',
      deadline: '2024-12-31',
      progress: 40
    },
    {
      id: 'goal-002',
      title: 'Bench Press 200 lbs',
      target: 200,
      current: 185,
      unit: 'lbs',
      deadline: '2024-10-31',
      progress: 75
    }
  ]
};

// Notifications
export const notifications = [
  {
    id: 'notif-001',
    type: 'class-reminder',
    title: 'Class Reminder',
    message: 'Your HIIT Bootcamp class starts in 30 minutes',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    read: false
  },
  {
    id: 'notif-002',
    type: 'achievement',
    title: 'Achievement Unlocked!',
    message: 'You completed 10 workouts this month',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    read: false
  },
  {
    id: 'notif-003',
    type: 'maintenance',
    title: 'Equipment Maintenance',
    message: 'Elliptical #3 is temporarily out of service',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
    read: true
  }
];

export default {
  users,
  trainers,
  classes,
  membershipPlans,
  equipment,
  mealPlans,
  progressData,
  notifications
};
