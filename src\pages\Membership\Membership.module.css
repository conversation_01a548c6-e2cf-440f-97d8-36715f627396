.membership {
  min-height: 100vh;
  background: var(--gray-50);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-6) var(--space-3);
}

/* Header Section */
.header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.125rem;
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Plans Grid */
.plansGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-12);
}

.planCard {
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 2px solid var(--gray-200);
  position: relative;
  padding: var(--space-6);
}

.planCard:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.planCard.popular {
  border-color: var(--primary-500);
  transform: scale(1.05);
}

.planCard.popular:hover {
  transform: scale(1.05) translateY(-4px);
}

.popularBadge {
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
  color: white;
  padding: var(--space-2) var(--space-4);
  border-radius: 0 0 var(--radius-md) var(--radius-md);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.planHeader {
  text-align: center;
  margin-bottom: var(--space-6);
  padding-top: var(--space-4);
}

.planName {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-3);
}

.planPrice {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: var(--space-1);
}

.price {
  font-size: 3rem;
  font-weight: 800;
  color: var(--primary-600);
}

.billing {
  font-size: 1rem;
  color: var(--gray-500);
  font-weight: 500;
}

/* Plan Features */
.planFeatures {
  margin-bottom: var(--space-6);
}

.featuresTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.featuresList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.feature {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: 0.875rem;
  color: var(--gray-700);
}

.checkIcon {
  color: var(--success-500);
  font-weight: 600;
  font-size: 1rem;
  flex-shrink: 0;
}

.planActions {
  margin-top: auto;
}

/* Benefits Section */
.benefits {
  text-align: center;
}

.benefitsTitle {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-8);
}

.benefitsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
}

.benefit {
  text-align: center;
  padding: var(--space-6);
}

.benefitIcon {
  font-size: 3rem;
  margin-bottom: var(--space-4);
}

.benefitTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-3);
}

.benefitText {
  font-size: 0.875rem;
  color: var(--gray-600);
  line-height: 1.6;
}

/* Responsive Design */
@media (min-width: 768px) {
  .container {
    padding: var(--space-8) var(--space-4);
  }
  
  .title {
    font-size: 2.5rem;
  }
  
  .benefitsTitle {
    font-size: 2.5rem;
  }
  
  .plansGrid {
    gap: var(--space-8);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: var(--space-12) var(--space-6);
  }
  
  .title {
    font-size: 3rem;
  }
  
  .benefitsTitle {
    font-size: 3rem;
  }
  
  .plansGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 767px) {
  .plansGrid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .planCard.popular {
    transform: none;
  }
  
  .planCard.popular:hover {
    transform: translateY(-4px);
  }
  
  .price {
    font-size: 2.5rem;
  }
  
  .benefitsGrid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .benefit {
    padding: var(--space-4);
  }
  
  .benefitIcon {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .planHeader {
    padding-top: var(--space-6);
  }
  
  .price {
    font-size: 2rem;
  }
  
  .planName {
    font-size: 1.25rem;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .planCard {
    border: 2px solid #000;
  }
  
  .title,
  .benefitsTitle {
    -webkit-text-fill-color: #000;
    color: #000;
  }
  
  .popularBadge {
    background: #000;
    border: 1px solid #fff;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .planCard {
    transition: none;
    transform: none;
  }
  
  .planCard:hover,
  .planCard.popular,
  .planCard.popular:hover {
    transform: none;
  }
}
