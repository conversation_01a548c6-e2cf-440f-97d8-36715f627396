.home {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
  color: white;
  padding: var(--space-16) 0;
  min-height: 80vh;
  display: flex;
  align-items: center;
}

.heroContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-12);
  align-items: center;
}

.heroText {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin: 0;
}

.heroTitleAccent {
  background: linear-gradient(45deg, #fbbf24, #f59e0b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.heroDescription {
  font-size: 1.25rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.heroActions {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.heroImage {
  display: flex;
  justify-content: center;
  align-items: center;
}

.heroImg {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  object-fit: cover;
}

/* Stats Section */
.stats {
  background: white;
  padding: var(--space-12) 0;
  border-bottom: 1px solid var(--gray-200);
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-8);
}

.statItem {
  text-align: center;
}

.statNumber {
  font-size: 3rem;
  font-weight: 800;
  color: var(--primary-500);
  line-height: 1;
  margin-bottom: var(--space-2);
}

.statLabel {
  font-size: 1rem;
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Section Styling */
.features,
.trainers,
.classes {
  padding: var(--space-16) 0;
}

.features {
  background: var(--gray-50);
}

.trainers {
  background: white;
}

.classes {
  background: var(--gray-50);
}

.sectionHeader {
  text-align: center;
  margin-bottom: var(--space-12);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
}

.sectionDescription {
  font-size: 1.125rem;
  color: var(--gray-600);
  line-height: 1.6;
  margin: 0;
}

.sectionFooter {
  text-align: center;
  margin-top: var(--space-12);
}

/* Features Grid */
.featuresGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-8);
}

.featureCard {
  text-align: center;
  padding: var(--space-8);
}

.featureIcon {
  font-size: 3rem;
  margin-bottom: var(--space-4);
}

.featureTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-3);
}

.featureDescription {
  color: var(--gray-600);
  line-height: 1.6;
  margin: 0;
}

/* Trainers Grid */
.trainersGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-8);
}

.trainerCard {
  padding: 0;
  overflow: hidden;
}

.trainerImage {
  width: 100%;
  height: 250px;
  object-fit: cover;
}

.trainerInfo {
  padding: var(--space-6);
}

.trainerName {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-3);
}

.trainerSpecialties {
  display: flex;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
  flex-wrap: wrap;
}

.specialtyTag {
  background: var(--primary-100);
  color: var(--primary-700);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  font-weight: 500;
}

.trainerRating {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.rating {
  font-weight: 600;
  color: var(--gray-900);
}

.reviews {
  font-size: 0.875rem;
  color: var(--gray-500);
}

/* Classes Grid */
.classesGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-8);
}

.classCard {
  padding: var(--space-6);
}

.classHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
}

.className {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.classDifficulty {
  background: var(--warning-100);
  color: var(--warning-700);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  font-weight: 500;
}

.classDescription {
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: var(--space-4);
}

.classDetails {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  margin-bottom: var(--space-6);
}

.classDetail {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detailLabel {
  font-size: 0.875rem;
  color: var(--gray-500);
  font-weight: 500;
}

.detailValue {
  font-size: 0.875rem;
  color: var(--gray-900);
  font-weight: 600;
}

/* CTA Section */
.cta {
  padding: var(--space-16) 0;
  background: var(--gray-100);
}

.ctaCard {
  text-align: center;
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: var(--space-4);
}

.ctaDescription {
  font-size: 1.125rem;
  line-height: 1.6;
  margin-bottom: var(--space-8);
  opacity: 0.9;
}

.ctaActions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .heroContent {
    grid-template-columns: 1fr;
    gap: var(--space-8);
    text-align: center;
  }
  
  .heroTitle {
    font-size: 3rem;
  }
  
  .featuresGrid,
  .trainersGrid,
  .classesGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
  
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
}

@media (max-width: 768px) {
  .hero {
    padding: var(--space-12) 0;
    min-height: 60vh;
  }
  
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .heroDescription {
    font-size: 1.125rem;
  }
  
  .heroActions {
    justify-content: center;
  }
  
  .sectionTitle {
    font-size: 2rem;
  }
  
  .featuresGrid,
  .trainersGrid,
  .classesGrid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
  }
  
  .statNumber {
    font-size: 2.5rem;
  }
  
  .ctaTitle {
    font-size: 2rem;
  }
  
  .ctaActions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--space-3);
  }
  
  .heroContent {
    padding: 0 var(--space-3);
  }
  
  .heroTitle {
    font-size: 2rem;
  }
  
  .heroDescription {
    font-size: 1rem;
  }
  
  .heroActions {
    flex-direction: column;
    align-items: center;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .sectionTitle {
    font-size: 1.75rem;
  }
  
  .ctaTitle {
    font-size: 1.75rem;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .heroTitleAccent {
    -webkit-text-fill-color: #fff;
    color: #fff;
  }
  
  .specialtyTag,
  .classDifficulty {
    border: 1px solid #000;
  }
}
