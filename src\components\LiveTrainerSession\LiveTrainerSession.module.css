.liveTrainerSession {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.sessionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding: 1.5rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.sessionTitle {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sessionBadge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.3);
}

.liveDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: white;
  animation: pulse 2s infinite;
}

.liveText {
  font-weight: 700;
}

.sessionContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.leftPanel,
.rightPanel {
  display: flex;
  flex-direction: column;
}

.sessionFooter {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.sessionInfo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.infoCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.infoCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.infoIcon {
  font-size: 2rem;
  flex-shrink: 0;
}

.infoContent {
  flex: 1;
  min-width: 0;
}

.infoLabel {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.infoValue {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.quickActions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.actionBtn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
  justify-content: center;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.actionBtn:hover {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.1), 0 2px 4px -1px rgba(102, 126, 234, 0.06);
}

.actionBtn:first-child {
  border-color: #fca5a5;
  color: #dc2626;
}

.actionBtn:first-child:hover {
  border-color: #ef4444;
  color: #dc2626;
  box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.1), 0 2px 4px -1px rgba(239, 68, 68, 0.06);
}

.actionBtn:nth-child(2) {
  border-color: #fde68a;
  color: #d97706;
}

.actionBtn:nth-child(2):hover {
  border-color: #f59e0b;
  color: #d97706;
  box-shadow: 0 4px 6px -1px rgba(245, 158, 11, 0.1), 0 2px 4px -1px rgba(245, 158, 11, 0.06);
}

.actionBtn:last-child {
  border-color: #a7f3d0;
  color: #059669;
}

.actionBtn:last-child:hover {
  border-color: #10b981;
  color: #059669;
  box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.1), 0 2px 4px -1px rgba(16, 185, 129, 0.06);
}

.actionIcon {
  width: 1.25rem;
  height: 1.25rem;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Mobile responsiveness */
@media (max-width: 1024px) {
  .liveTrainerSession {
    padding: 1.5rem;
  }
  
  .sessionContent {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .liveTrainerSession {
    padding: 1rem;
  }
  
  .sessionHeader {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 1.5rem;
    text-align: center;
  }
  
  .sessionTitle {
    font-size: 1.5rem;
  }
  
  .sessionInfo {
    grid-template-columns: 1fr;
  }
  
  .quickActions {
    flex-direction: column;
    align-items: center;
  }
  
  .actionBtn {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .sessionHeader {
    padding: 1rem;
  }
  
  .sessionTitle {
    font-size: 1.25rem;
  }
  
  .infoCard {
    padding: 1rem;
  }
  
  .infoIcon {
    font-size: 1.5rem;
  }
  
  .actionBtn {
    padding: 0.875rem 1rem;
    font-size: 0.8rem;
  }
  
  .actionIcon {
    width: 1rem;
    height: 1rem;
  }
}

/* High contrast mode for workout environments */
@media (prefers-contrast: high) {
  .sessionHeader,
  .infoCard,
  .actionBtn {
    border: 2px solid #000;
  }
  
  .sessionTitle {
    color: #000;
    -webkit-text-fill-color: #000;
  }
  
  .infoValue {
    color: #000;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .liveDot,
  .infoCard,
  .actionBtn {
    animation: none;
    transition: none;
  }
}

/* Focus styles for accessibility */
.actionBtn:focus {
  outline: 3px solid #667eea;
  outline-offset: 2px;
}

.actionBtn:focus:not(:focus-visible) {
  outline: none;
}

/* Touch-friendly sizing */
@media (pointer: coarse) {
  .actionBtn {
    min-height: 48px;
    padding: 1rem 1.5rem;
  }
}
