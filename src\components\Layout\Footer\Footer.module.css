.footer {
  background: var(--gray-900);
  color: var(--gray-300);
  margin-top: auto;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-12) var(--space-4) var(--space-6);
}

.footerContent {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: var(--space-12);
  margin-bottom: var(--space-12);
}

/* Brand Section */
.brandSection {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.logoIcon {
  font-size: 2rem;
  line-height: 1;
}

.logoText {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  letter-spacing: -0.025em;
}

.brandDescription {
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--gray-400);
  margin: 0;
}

.socialLinks {
  display: flex;
  gap: var(--space-4);
}

.socialLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--gray-800);
  border-radius: var(--radius-md);
  color: var(--gray-400);
  transition: all var(--transition-fast);
}

.socialLink:hover {
  background: var(--primary-500);
  color: white;
  transform: translateY(-2px);
}

.socialLink svg {
  width: 1.25rem;
  height: 1.25rem;
}

/* Links Section */
.linksSection {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-8);
}

.linkGroup {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.linkGroupTitle {
  font-size: 1rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.linkList {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.footerLink {
  color: var(--gray-400);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color var(--transition-fast);
}

.footerLink:hover {
  color: white;
}

/* Contact Section */
.contactSection {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.contactTitle {
  font-size: 1rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.contactInfo {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.contactItem {
  display: flex;
  gap: var(--space-3);
  align-items: flex-start;
}

.contactIcon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--primary-500);
  flex-shrink: 0;
  margin-top: 2px;
}

.contactLabel {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-1);
}

.contactValue {
  font-size: 0.875rem;
  color: var(--gray-300);
  line-height: 1.5;
}

/* Footer Bottom */
.footerBottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: var(--space-6);
  border-top: 1px solid var(--gray-800);
}

.copyright {
  margin: 0;
}

.copyright p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--gray-500);
}

.legalLinks {
  display: flex;
  gap: var(--space-6);
}

.legalLink {
  color: var(--gray-500);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color var(--transition-fast);
}

.legalLink:hover {
  color: var(--gray-300);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footerContent {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-8);
  }
  
  .contactSection {
    grid-column: 1 / -1;
  }
  
  .linksSection {
    grid-template-columns: repeat(4, 1fr);
    grid-column: 1 / -1;
  }
}

@media (max-width: 768px) {
  .container {
    padding: var(--space-8) var(--space-3) var(--space-6);
  }
  
  .footerContent {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }
  
  .linksSection {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
  
  .footerBottom {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }
  
  .legalLinks {
    gap: var(--space-4);
  }
}

@media (max-width: 480px) {
  .container {
    padding: var(--space-6) var(--space-3) var(--space-4);
  }
  
  .footerContent {
    gap: var(--space-6);
  }
  
  .linksSection {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .socialLinks {
    justify-content: center;
  }
  
  .brandDescription {
    text-align: center;
  }
  
  .legalLinks {
    flex-direction: column;
    gap: var(--space-2);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .footer {
    border-top: 2px solid #fff;
  }
  
  .footerLink,
  .legalLink {
    border-bottom: 1px solid transparent;
  }
  
  .footerLink:hover,
  .legalLink:hover {
    border-bottom-color: #fff;
  }
  
  .socialLink {
    border: 1px solid var(--gray-600);
  }
  
  .socialLink:hover {
    border-color: var(--primary-500);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .socialLink,
  .footerLink,
  .legalLink {
    transition: none;
  }
  
  .socialLink:hover {
    transform: none;
  }
}
