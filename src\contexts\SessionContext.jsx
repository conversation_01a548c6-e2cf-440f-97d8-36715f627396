import { createContext, useContext, useState, useCallback } from 'react';
import { useTimer } from '../hooks/useTimer';

const SessionContext = createContext();

export const useSession = () => {
  const context = useContext(SessionContext);
  if (!context) {
    throw new Error('useSession must be used within a SessionProvider');
  }
  return context;
};

export const SessionProvider = ({ children }) => {
  // Session configuration
  const [sessionConfig, setSessionConfig] = useState({
    duration: 3600, // 1 hour default
    type: 'strength-training',
    intensity: 'moderate'
  });
  
  // Trainer information
  const [trainer, setTrainer] = useState({
    id: 'trainer-001',
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=150&h=150&fit=crop&crop=face',
    credentials: ['NASM-CPT', 'ACSM-EP'],
    rating: 4.9,
    reviewCount: 127,
    specialties: ['Strength Training', 'HIIT', 'Functional Fitness'],
    isOnline: true,
    currentFocus: 'Upper Body Strength'
  });
  
  // Session state
  const [sessionState, setSessionState] = useState({
    isActive: false,
    startTime: null,
    participants: 1,
    sessionId: `session-${Date.now()}`
  });
  
  // Timer configuration
  const timerConfig = {
    initialTime: 0, // Count up timer
    autoStart: false,
    onTick: (time) => {
      // Could trigger events on specific intervals
      if (time % 300 === 0 && time > 0) { // Every 5 minutes
        console.log(`Session milestone: ${time / 60} minutes completed`);
      }
    },
    onComplete: () => {
      console.log('Session timer completed');
    }
  };
  
  const timer = useTimer(timerConfig);
  
  // Start session
  const startSession = useCallback(() => {
    setSessionState(prev => ({
      ...prev,
      isActive: true,
      startTime: new Date()
    }));
    timer.start();
  }, [timer]);
  
  // End session
  const endSession = useCallback(() => {
    setSessionState(prev => ({
      ...prev,
      isActive: false
    }));
    timer.stop();
  }, [timer]);
  
  // Pause session
  const pauseSession = useCallback(() => {
    timer.pause();
  }, [timer]);
  
  // Resume session
  const resumeSession = useCallback(() => {
    timer.resume();
  }, [timer]);
  
  // Update session configuration
  const updateSessionConfig = useCallback((newConfig) => {
    setSessionConfig(prev => ({ ...prev, ...newConfig }));
  }, []);
  
  // Update trainer information
  const updateTrainer = useCallback((newTrainerInfo) => {
    setTrainer(prev => ({ ...prev, ...newTrainerInfo }));
  }, []);
  
  // Get session statistics
  const getSessionStats = useCallback(() => {
    const duration = timer.time;
    const isActive = sessionState.isActive && timer.isRunning;
    
    return {
      duration,
      isActive,
      isPaused: timer.isPaused,
      startTime: sessionState.startTime,
      formattedDuration: formatDuration(duration),
      sessionId: sessionState.sessionId
    };
  }, [timer, sessionState]);
  
  // Format duration helper
  const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };
  
  const value = {
    // State
    sessionConfig,
    trainer,
    sessionState,
    timer,
    
    // Actions
    startSession,
    endSession,
    pauseSession,
    resumeSession,
    updateSessionConfig,
    updateTrainer,
    
    // Computed
    getSessionStats
  };
  
  return (
    <SessionContext.Provider value={value}>
      {children}
    </SessionContext.Provider>
  );
};
