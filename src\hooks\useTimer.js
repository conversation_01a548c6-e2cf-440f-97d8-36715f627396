import { useState, useEffect, useRef, useCallback } from 'react';

/**
 * Custom hook for managing session timer functionality
 * @param {Object} options - Timer configuration options
 * @param {number} options.initialTime - Initial time in seconds (0 for count up, >0 for countdown)
 * @param {boolean} options.autoStart - Whether to start timer automatically
 * @param {Function} options.onComplete - Callback when timer completes (countdown reaches 0)
 * @param {Function} options.onTick - Callback on each second tick
 * @returns {Object} Timer state and controls
 */
export const useTimer = ({
  initialTime = 0,
  autoStart = false,
  onComplete = null,
  onTick = null
} = {}) => {
  const [time, setTime] = useState(initialTime);
  const [isRunning, setIsRunning] = useState(autoStart);
  const [isPaused, setIsPaused] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  
  const intervalRef = useRef(null);
  const startTimeRef = useRef(null);
  const pausedTimeRef = useRef(0);
  
  const isCountdown = initialTime > 0;
  
  // Clear interval on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);
  
  // Timer logic
  useEffect(() => {
    if (isRunning && !isPaused && !isCompleted) {
      intervalRef.current = setInterval(() => {
        setTime(prevTime => {
          let newTime;
          
          if (isCountdown) {
            // Countdown timer
            newTime = Math.max(0, prevTime - 1);
            
            if (newTime === 0) {
              setIsCompleted(true);
              setIsRunning(false);
              if (onComplete) {
                onComplete();
              }
            }
          } else {
            // Count up timer
            newTime = prevTime + 1;
          }
          
          if (onTick) {
            onTick(newTime);
          }
          
          return newTime;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, isPaused, isCompleted, isCountdown, onComplete, onTick]);
  
  // Start timer
  const start = useCallback(() => {
    if (!isCompleted) {
      setIsRunning(true);
      setIsPaused(false);
      if (!startTimeRef.current) {
        startTimeRef.current = Date.now();
      }
    }
  }, [isCompleted]);
  
  // Pause timer
  const pause = useCallback(() => {
    setIsPaused(true);
    setIsRunning(false);
    pausedTimeRef.current = Date.now();
  }, []);
  
  // Resume timer
  const resume = useCallback(() => {
    if (isPaused && !isCompleted) {
      setIsPaused(false);
      setIsRunning(true);
    }
  }, [isPaused, isCompleted]);
  
  // Stop timer
  const stop = useCallback(() => {
    setIsRunning(false);
    setIsPaused(false);
    setIsCompleted(true);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);
  
  // Reset timer
  const reset = useCallback(() => {
    setTime(initialTime);
    setIsRunning(false);
    setIsPaused(false);
    setIsCompleted(false);
    startTimeRef.current = null;
    pausedTimeRef.current = 0;
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, [initialTime]);
  
  // Toggle play/pause
  const toggle = useCallback(() => {
    if (isRunning) {
      pause();
    } else if (isPaused) {
      resume();
    } else {
      start();
    }
  }, [isRunning, isPaused, start, pause, resume]);
  
  // Get session duration (total time elapsed since start)
  const getSessionDuration = useCallback(() => {
    if (!startTimeRef.current) return 0;
    
    const now = Date.now();
    const totalElapsed = Math.floor((now - startTimeRef.current) / 1000);
    
    if (isPaused && pausedTimeRef.current) {
      const pausedDuration = Math.floor((now - pausedTimeRef.current) / 1000);
      return totalElapsed - pausedDuration;
    }
    
    return totalElapsed;
  }, [isPaused]);
  
  return {
    time,
    isRunning,
    isPaused,
    isCompleted,
    isCountdown,
    start,
    pause,
    resume,
    stop,
    reset,
    toggle,
    getSessionDuration
  };
};
