import { useState } from 'react';
import { equipment } from '../../data/mockData';
import Card from '../../components/UI/Card/Card';
import styles from './Equipment.module.css';

const Equipment = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  
  const categories = ['all', 'Cardio', 'Strength'];
  
  const filteredEquipment = equipment.filter(item => 
    selectedCategory === 'all' || item.category === selectedCategory
  );

  const getStatusColor = (status) => {
    switch (status) {
      case 'available': return 'success';
      case 'in-use': return 'warning';
      case 'maintenance': return 'error';
      default: return 'default';
    }
  };

  return (
    <div className={styles.equipment}>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>Gym Equipment</h1>
          <p className={styles.subtitle}>
            Check availability and status of our fitness equipment
          </p>
        </div>

        <div className={styles.filters}>
          {categories.map(category => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`${styles.filterBtn} ${
                selectedCategory === category ? styles.active : ''
              }`}
            >
              {category === 'all' ? 'All Equipment' : category}
            </button>
          ))}
        </div>

        <div className={styles.equipmentGrid}>
          {filteredEquipment.map(item => (
            <Card key={item.id} hover className={styles.equipmentCard}>
              <img
                src={item.image}
                alt={item.name}
                className={styles.equipmentImage}
              />
              
              <div className={styles.equipmentInfo}>
                <div className={styles.equipmentHeader}>
                  <h3 className={styles.equipmentName}>{item.name}</h3>
                  <span className={`${styles.status} ${styles[getStatusColor(item.status)]}`}>
                    {item.status.replace('-', ' ')}
                  </span>
                </div>
                
                <div className={styles.equipmentDetails}>
                  <p className={styles.brand}>{item.brand} - {item.model}</p>
                  <p className={styles.location}>📍 {item.location}</p>
                </div>

                <div className={styles.features}>
                  <h4 className={styles.featuresTitle}>Features:</h4>
                  <ul className={styles.featuresList}>
                    {item.features.map((feature, index) => (
                      <li key={index} className={styles.feature}>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className={styles.maintenance}>
                  <div className={styles.maintenanceItem}>
                    <span className={styles.label}>Last Maintenance:</span>
                    <span className={styles.value}>
                      {new Date(item.lastMaintenance).toLocaleDateString()}
                    </span>
                  </div>
                  <div className={styles.maintenanceItem}>
                    <span className={styles.label}>Next Maintenance:</span>
                    <span className={styles.value}>
                      {new Date(item.nextMaintenance).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Equipment;
