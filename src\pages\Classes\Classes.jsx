import { useState } from 'react';
import { classes } from '../../data/mockData';
import Button from '../../components/UI/Button/Button';
import Card from '../../components/UI/Card/Card';
import styles from './Classes.module.css';

const Classes = () => {
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  
  const difficulties = ['all', 'Beginner', 'Intermediate', 'Advanced'];
  
  const filteredClasses = classes.filter(classItem => 
    selectedDifficulty === 'all' || classItem.difficulty === selectedDifficulty
  );

  return (
    <div className={styles.classes}>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>Group Fitness Classes</h1>
          <p className={styles.subtitle}>
            Join our energizing group classes and train with the community
          </p>
        </div>

        <div className={styles.filters}>
          {difficulties.map(difficulty => (
            <button
              key={difficulty}
              onClick={() => setSelectedDifficulty(difficulty)}
              className={`${styles.filterBtn} ${
                selectedDifficulty === difficulty ? styles.active : ''
              }`}
            >
              {difficulty === 'all' ? 'All Levels' : difficulty}
            </button>
          ))}
        </div>

        <div className={styles.classesGrid}>
          {filteredClasses.map(classItem => (
            <Card key={classItem.id} hover className={styles.classCard}>
              <div className={styles.classHeader}>
                <h3 className={styles.className}>{classItem.name}</h3>
                <span className={styles.difficulty}>{classItem.difficulty}</span>
              </div>
              
              <p className={styles.description}>{classItem.description}</p>
              
              <div className={styles.classDetails}>
                <div className={styles.detail}>
                  <span className={styles.label}>Duration:</span>
                  <span className={styles.value}>{classItem.duration} min</span>
                </div>
                <div className={styles.detail}>
                  <span className={styles.label}>Capacity:</span>
                  <span className={styles.value}>
                    {classItem.currentEnrollment}/{classItem.maxCapacity}
                  </span>
                </div>
                <div className={styles.detail}>
                  <span className={styles.label}>Price:</span>
                  <span className={styles.value}>${classItem.price}</span>
                </div>
              </div>

              <div className={styles.schedule}>
                <h4 className={styles.scheduleTitle}>Upcoming Sessions</h4>
                {classItem.schedule.slice(0, 3).map((session, index) => (
                  <div key={index} className={styles.session}>
                    <span className={styles.day}>{session.day}</span>
                    <span className={styles.time}>{session.time}</span>
                  </div>
                ))}
              </div>

              <Button fullWidth>
                Book Class
              </Button>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Classes;
