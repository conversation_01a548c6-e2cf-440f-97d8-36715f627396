.button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-family: inherit;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 44px; /* Touch-friendly */
  white-space: nowrap;
}

.button:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Variants */
.primary {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
}

.primary:hover:not(:disabled) {
  background: var(--primary-600);
  border-color: var(--primary-600);
  transform: translateY(-1px);
}

.secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  border-color: var(--gray-300);
}

.secondary:hover:not(:disabled) {
  background: var(--gray-200);
  border-color: var(--gray-400);
  transform: translateY(-1px);
}

.outline {
  background: transparent;
  color: var(--primary-500);
  border-color: var(--primary-500);
}

.outline:hover:not(:disabled) {
  background: var(--primary-50);
  transform: translateY(-1px);
}

.ghost {
  background: transparent;
  color: var(--gray-700);
  border-color: transparent;
}

.ghost:hover:not(:disabled) {
  background: var(--gray-100);
}

.danger {
  background: var(--error-500);
  color: white;
  border-color: var(--error-500);
}

.danger:hover:not(:disabled) {
  background: var(--error-600);
  border-color: var(--error-600);
  transform: translateY(-1px);
}

.success {
  background: var(--success-500);
  color: white;
  border-color: var(--success-500);
}

.success:hover:not(:disabled) {
  background: var(--success-600);
  border-color: var(--success-600);
  transform: translateY(-1px);
}

/* Sizes */
.small {
  padding: var(--space-2) var(--space-3);
  font-size: 0.75rem;
  min-height: 36px;
}

.medium {
  padding: var(--space-3) var(--space-4);
  font-size: 0.875rem;
}

.large {
  padding: var(--space-4) var(--space-6);
  font-size: 1rem;
  min-height: 52px;
}

/* Full width */
.fullWidth {
  width: 100%;
}

/* Loading state */
.loading {
  pointer-events: none;
}

.spinner {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinnerIcon {
  width: 1rem;
  height: 1rem;
  animation: spin 1s linear infinite;
}

.hiddenText {
  opacity: 0;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .button {
    border-width: 2px;
  }
  
  .primary,
  .danger,
  .success {
    border-color: #000;
  }
  
  .secondary,
  .outline,
  .ghost {
    border-color: #000;
    color: #000;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .button {
    transition: none;
  }
  
  .button:hover:not(:disabled) {
    transform: none;
  }
  
  .spinnerIcon {
    animation: none;
  }
}

/* Touch devices */
@media (pointer: coarse) {
  .button {
    min-height: 48px;
  }
  
  .small {
    min-height: 40px;
    padding: var(--space-3) var(--space-4);
  }
  
  .large {
    min-height: 56px;
  }
}
