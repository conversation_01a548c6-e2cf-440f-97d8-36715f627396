import { progressData } from '../../data/mockData';
import Card from '../../components/UI/Card/Card';
import styles from './Progress.module.css';

const Progress = () => {
  const { workouts, measurements, goals } = progressData;

  return (
    <div className={styles.progress}>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>Your Progress</h1>
          <p className={styles.subtitle}>
            Track your fitness journey and celebrate your achievements
          </p>
        </div>

        <div className={styles.progressGrid}>
          {/* Goals */}
          <Card className={styles.goalsCard}>
            <h2 className={styles.cardTitle}>Current Goals</h2>
            <div className={styles.goalsList}>
              {goals.map(goal => (
                <div key={goal.id} className={styles.goal}>
                  <div className={styles.goalHeader}>
                    <h3 className={styles.goalTitle}>{goal.title}</h3>
                    <span className={styles.goalProgress}>{goal.progress}%</span>
                  </div>
                  <div className={styles.progressBar}>
                    <div 
                      className={styles.progressFill}
                      style={{ width: `${goal.progress}%` }}
                    />
                  </div>
                  <div className={styles.goalDetails}>
                    <span className={styles.current}>{goal.current} {goal.unit}</span>
                    <span className={styles.target}>Target: {goal.target} {goal.unit}</span>
                  </div>
                  <div className={styles.deadline}>
                    Deadline: {new Date(goal.deadline).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Recent Workouts */}
          <Card className={styles.workoutsCard}>
            <h2 className={styles.cardTitle}>Recent Workouts</h2>
            <div className={styles.workoutsList}>
              {workouts.map((workout, index) => (
                <div key={index} className={styles.workout}>
                  <div className={styles.workoutIcon}>
                    {workout.type === 'Strength' && '🏋️‍♂️'}
                    {workout.type === 'Cardio' && '🏃‍♂️'}
                    {workout.type === 'HIIT' && '⚡'}
                    {workout.type === 'Yoga' && '🧘‍♀️'}
                  </div>
                  <div className={styles.workoutDetails}>
                    <div className={styles.workoutType}>{workout.type}</div>
                    <div className={styles.workoutMeta}>
                      {workout.duration} min • {workout.calories} cal
                    </div>
                  </div>
                  <div className={styles.workoutDate}>
                    {new Date(workout.date).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Body Measurements */}
          <Card className={styles.measurementsCard}>
            <h2 className={styles.cardTitle}>Body Measurements</h2>
            <div className={styles.measurementsList}>
              {measurements.map((measurement, index) => (
                <div key={index} className={styles.measurement}>
                  <div className={styles.measurementDate}>
                    {new Date(measurement.date).toLocaleDateString()}
                  </div>
                  <div className={styles.measurementData}>
                    <div className={styles.dataItem}>
                      <span className={styles.label}>Weight:</span>
                      <span className={styles.value}>{measurement.weight} lbs</span>
                    </div>
                    <div className={styles.dataItem}>
                      <span className={styles.label}>Body Fat:</span>
                      <span className={styles.value}>{measurement.bodyFat}%</span>
                    </div>
                    <div className={styles.dataItem}>
                      <span className={styles.label}>Muscle:</span>
                      <span className={styles.value}>{measurement.muscle} lbs</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Statistics */}
          <Card className={styles.statsCard}>
            <h2 className={styles.cardTitle}>Statistics</h2>
            <div className={styles.statsList}>
              <div className={styles.stat}>
                <div className={styles.statIcon}>🏋️‍♂️</div>
                <div className={styles.statContent}>
                  <div className={styles.statValue}>{workouts.length}</div>
                  <div className={styles.statLabel}>Total Workouts</div>
                </div>
              </div>
              <div className={styles.stat}>
                <div className={styles.statIcon}>⏱️</div>
                <div className={styles.statContent}>
                  <div className={styles.statValue}>
                    {workouts.reduce((total, w) => total + w.duration, 0)} min
                  </div>
                  <div className={styles.statLabel}>Total Time</div>
                </div>
              </div>
              <div className={styles.stat}>
                <div className={styles.statIcon}>🔥</div>
                <div className={styles.statContent}>
                  <div className={styles.statValue}>
                    {workouts.reduce((total, w) => total + w.calories, 0)}
                  </div>
                  <div className={styles.statLabel}>Calories Burned</div>
                </div>
              </div>
              <div className={styles.stat}>
                <div className={styles.statIcon}>📈</div>
                <div className={styles.statContent}>
                  <div className={styles.statValue}>
                    {Math.round(goals.reduce((total, g) => total + g.progress, 0) / goals.length)}%
                  </div>
                  <div className={styles.statLabel}>Avg Goal Progress</div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Progress;
