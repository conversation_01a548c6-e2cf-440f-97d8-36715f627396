.nutrition {
  min-height: 100vh;
  background: var(--gray-50);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-6) var(--space-3);
}

/* Header Section */
.header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.125rem;
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Plans Grid */
.plansGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-12);
}

.planCard {
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
  padding: var(--space-6);
}

.planCard:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.planHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
  gap: var(--space-3);
}

.planName {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
  flex: 1;
}

.planPrice {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-600);
  flex-shrink: 0;
}

.planDescription {
  font-size: 0.875rem;
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: var(--space-4);
}

.planStats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.stat {
  text-align: center;
}

.statValue {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-1);
}

.statLabel {
  font-size: 0.75rem;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Meals */
.meals {
  margin-bottom: var(--space-6);
}

.mealsTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.meal {
  padding: var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-3);
}

.meal:last-child {
  margin-bottom: 0;
}

.mealHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.mealName {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-900);
}

.mealCalories {
  font-size: 0.75rem;
  color: var(--primary-600);
  font-weight: 600;
}

.mealItems {
  font-size: 0.75rem;
  color: var(--gray-600);
  line-height: 1.5;
}

.planMeta {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-bottom: var(--space-6);
}

.duration {
  font-size: 0.875rem;
  color: var(--gray-700);
  font-weight: 500;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.tag {
  padding: var(--space-1) var(--space-2);
  background: var(--primary-100);
  color: var(--primary-700);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

/* Features Section */
.features {
  text-align: center;
}

.featuresTitle {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-8);
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
}

.feature {
  text-align: center;
  padding: var(--space-6);
}

.featureIcon {
  font-size: 3rem;
  margin-bottom: var(--space-4);
}

.featureTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-3);
}

.featureText {
  font-size: 0.875rem;
  color: var(--gray-600);
  line-height: 1.6;
}

/* Responsive Design */
@media (min-width: 768px) {
  .container {
    padding: var(--space-8) var(--space-4);
  }
  
  .title {
    font-size: 2.5rem;
  }
  
  .featuresTitle {
    font-size: 2.5rem;
  }
  
  .plansGrid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--space-8);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: var(--space-12) var(--space-6);
  }
  
  .title {
    font-size: 3rem;
  }
  
  .featuresTitle {
    font-size: 3rem;
  }
  
  .plansGrid {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  }
}

@media (max-width: 767px) {
  .plansGrid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .planHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  .planStats {
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }
  
  .mealHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }
  
  .planMeta {
    gap: var(--space-2);
  }
  
  .featuresGrid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .feature {
    padding: var(--space-4);
  }
  
  .featureIcon {
    font-size: 2.5rem;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .planCard {
    border: 2px solid #000;
  }
  
  .title,
  .featuresTitle {
    -webkit-text-fill-color: #000;
    color: #000;
  }
  
  .tag {
    border: 1px solid #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .planCard {
    transition: none;
    transform: none;
  }
  
  .planCard:hover {
    transform: none;
  }
}
