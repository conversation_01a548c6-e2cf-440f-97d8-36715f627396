.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--gray-50);
}

.app-header {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
  color: white;
  padding: var(--space-4) var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.app-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.toggle-session-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-weight: 500;
  min-height: 44px;
  font-size: 0.875rem;
}

.toggle-session-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.toggle-session-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

.app-main {
  flex: 1;
  padding: 0;
  background: var(--gray-50);
  min-height: calc(100vh - 80px);
}

/* Responsive Design */
@media (min-width: 768px) {
  .app-header {
    padding: var(--space-4) var(--space-8);
  }

  .app-header h1 {
    font-size: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .app-header h1 {
    font-size: 2rem;
  }
}

@media (max-width: 767px) {
  .app-header {
    padding: var(--space-3) var(--space-4);
    flex-direction: column;
    gap: var(--space-3);
    position: relative;
  }

  .app-header h1 {
    font-size: 1.25rem;
  }

  .toggle-session-btn {
    width: 100%;
    max-width: 200px;
  }

  .app-main {
    min-height: calc(100vh - 120px);
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: var(--space-3);
  }

  .app-header h1 {
    font-size: 1.125rem;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .app-header {
    border-bottom: 2px solid #000;
  }

  .toggle-session-btn {
    border: 2px solid rgba(255, 255, 255, 0.8);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .toggle-session-btn {
    transition: none;
  }

  .toggle-session-btn:hover {
    transform: none;
  }
}

/* Touch Devices */
@media (pointer: coarse) {
  .toggle-session-btn {
    min-height: 48px;
    padding: var(--space-3) var(--space-5);
  }
}
