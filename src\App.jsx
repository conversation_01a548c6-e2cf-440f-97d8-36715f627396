import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Layout from './components/Layout/Layout';
import ProtectedRoute from './components/Auth/ProtectedRoute';

// Pages
import Home from './pages/Home/Home';
import Login from './pages/Auth/Login';
import Register from './pages/Auth/Register';
import Dashboard from './pages/Dashboard/Dashboard';
import LiveSessions from './pages/LiveSessions/LiveSessions';
import Trainers from './pages/Trainers/Trainers';
import Classes from './pages/Classes/Classes';
import Membership from './pages/Membership/Membership';
import Equipment from './pages/Equipment/Equipment';
import Nutrition from './pages/Nutrition/Nutrition';
import Progress from './pages/Progress/Progress';

import './App.css';

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public routes */}
          <Route path="/" element={<Layout />}>
            <Route index element={<Home />} />
            <Route path="login" element={<Login />} />
            <Route path="register" element={<Register />} />
            <Route path="membership" element={<Membership />} />

            {/* Protected routes */}
            <Route path="dashboard" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />
            <Route path="live-sessions" element={
              <ProtectedRoute>
                <LiveSessions />
              </ProtectedRoute>
            } />
            <Route path="trainers" element={
              <ProtectedRoute>
                <Trainers />
              </ProtectedRoute>
            } />
            <Route path="classes" element={
              <ProtectedRoute>
                <Classes />
              </ProtectedRoute>
            } />
            <Route path="equipment" element={
              <ProtectedRoute>
                <Equipment />
              </ProtectedRoute>
            } />
            <Route path="nutrition" element={
              <ProtectedRoute>
                <Nutrition />
              </ProtectedRoute>
            } />
            <Route path="progress" element={
              <ProtectedRoute>
                <Progress />
              </ProtectedRoute>
            } />

            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Route>
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
