import { useSession } from '../../../contexts/SessionContext';
import { useMessages } from '../../../contexts/MessageContext';
import styles from './TrainerProfile.module.css';

/**
 * Trainer Profile Component
 * Displays trainer information, credentials, and quick actions
 */
const TrainerProfile = () => {
  const { trainer } = useSession();
  const { showOverlay, unreadCount } = useMessages();
  
  const handleMessageTrainer = () => {
    showOverlay();
  };
  
  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <svg key={i} className={styles.star} viewBox="0 0 20 20" fill="currentColor">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      );
    }
    
    if (hasHalfStar) {
      stars.push(
        <svg key="half" className={styles.star} viewBox="0 0 20 20" fill="currentColor">
          <defs>
            <linearGradient id="half-star">
              <stop offset="50%" stopColor="currentColor" />
              <stop offset="50%" stopColor="#d1d5db" />
            </linearGradient>
          </defs>
          <path fill="url(#half-star)" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      );
    }
    
    return stars;
  };
  
  return (
    <div className={styles.trainerProfile}>
      <div className={styles.profileHeader}>
        <div className={styles.avatarContainer}>
          <img 
            src={trainer.avatar} 
            alt={`${trainer.name} profile`}
            className={styles.avatar}
            onError={(e) => {
              e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(trainer.name)}&background=667eea&color=fff&size=80`;
            }}
          />
          <div className={`${styles.statusIndicator} ${trainer.isOnline ? styles.online : styles.offline}`}>
            <div className={styles.statusDot}></div>
          </div>
        </div>
        
        <div className={styles.profileInfo}>
          <h2 className={styles.trainerName}>{trainer.name}</h2>
          <div className={styles.credentials}>
            {trainer.credentials.map((cert, index) => (
              <span key={index} className={styles.credential}>
                {cert}
              </span>
            ))}
          </div>
        </div>
      </div>
      
      <div className={styles.profileDetails}>
        <div className={styles.rating}>
          <div className={styles.stars}>
            {renderStars(trainer.rating)}
          </div>
          <span className={styles.ratingText}>
            {trainer.rating} ({trainer.reviewCount} reviews)
          </span>
        </div>
        
        <div className={styles.currentFocus}>
          <span className={styles.focusLabel}>Current Focus:</span>
          <span className={styles.focusValue}>{trainer.currentFocus}</span>
        </div>
        
        <div className={styles.specialties}>
          <span className={styles.specialtiesLabel}>Specialties:</span>
          <div className={styles.specialtyTags}>
            {trainer.specialties.map((specialty, index) => (
              <span key={index} className={styles.specialtyTag}>
                {specialty}
              </span>
            ))}
          </div>
        </div>
      </div>
      
      <div className={styles.actions}>
        <button 
          onClick={handleMessageTrainer}
          className={styles.messageBtn}
          aria-label="Message trainer"
        >
          <svg className={styles.icon} viewBox="0 0 24 24" fill="currentColor">
            <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
          </svg>
          Message
          {unreadCount > 0 && (
            <span className={styles.unreadBadge}>
              {unreadCount > 99 ? '99+' : unreadCount}
            </span>
          )}
        </button>
        
        <button className={styles.callBtn} aria-label="Call trainer">
          <svg className={styles.icon} viewBox="0 0 24 24" fill="currentColor">
            <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
          </svg>
          Call
        </button>
        
        <button className={styles.videoBtn} aria-label="Video call trainer">
          <svg className={styles.icon} viewBox="0 0 24 24" fill="currentColor">
            <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/>
          </svg>
          Video
        </button>
      </div>
    </div>
  );
};

export default TrainerProfile;
