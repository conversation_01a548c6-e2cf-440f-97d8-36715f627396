import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';
import styles from './Header.module.css';

const Header = () => {
  const { user, logout, isAuthenticated } = useAuth();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);

  const navigation = [
    { name: 'Home', href: '/', icon: '🏠' },
    { name: 'Live Sessions', href: '/live-sessions', icon: '🎥' },
    { name: 'Trainers', href: '/trainers', icon: '👨‍💼' },
    { name: 'Classes', href: '/classes', icon: '🏃‍♀️' },
    { name: 'Equipment', href: '/equipment', icon: '🏋️‍♂️' },
    { name: 'Nutrition', href: '/nutrition', icon: '🥗' },
    { name: 'Progress', href: '/progress', icon: '📊' }
  ];

  const handleLogout = () => {
    logout();
    setIsProfileMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const toggleProfileMenu = () => {
    setIsProfileMenuOpen(!isProfileMenuOpen);
  };

  const isActivePath = (path) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  return (
    <header className={styles.header}>
      <div className={styles.container}>
        {/* Logo */}
        <div className={styles.logo}>
          <Link to="/" className={styles.logoLink}>
            <div className={styles.logoIcon}>💪</div>
            <span className={styles.logoText}>FitnessGym</span>
          </Link>
        </div>

        {/* Desktop Navigation */}
        <nav className={styles.desktopNav}>
          {navigation.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className={`${styles.navLink} ${isActivePath(item.href) ? styles.active : ''}`}
            >
              <span className={styles.navIcon}>{item.icon}</span>
              <span className={styles.navText}>{item.name}</span>
            </Link>
          ))}
        </nav>

        {/* User Actions */}
        <div className={styles.userActions}>
          {isAuthenticated ? (
            <>
              {/* Notifications */}
              <button className={styles.notificationBtn} aria-label="Notifications">
                <svg className={styles.icon} viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
                </svg>
                <span className={styles.notificationBadge}>3</span>
              </button>

              {/* Profile Menu */}
              <div className={styles.profileMenu}>
                <button
                  onClick={toggleProfileMenu}
                  className={styles.profileBtn}
                  aria-label="Profile menu"
                >
                  <img
                    src={user.avatar}
                    alt={`${user.firstName} ${user.lastName}`}
                    className={styles.avatar}
                  />
                  <span className={styles.userName}>{user.firstName}</span>
                  <svg className={styles.chevron} viewBox="0 0 24 24" fill="currentColor">
                    <path d="M7 10l5 5 5-5z"/>
                  </svg>
                </button>

                {isProfileMenuOpen && (
                  <div className={styles.profileDropdown}>
                    <div className={styles.profileHeader}>
                      <img
                        src={user.avatar}
                        alt={`${user.firstName} ${user.lastName}`}
                        className={styles.dropdownAvatar}
                        onError={(e) => {
                          e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(user.firstName + ' ' + user.lastName)}&background=667eea&color=fff&size=150`;
                        }}
                      />
                      <div className={styles.profileInfo}>
                        <div className={styles.profileName}>
                          {user.firstName} {user.lastName}
                        </div>
                        <div className={styles.profileEmail}>{user.email}</div>
                      </div>
                    </div>

                    <div className={styles.profileActions}>
                      <Link to="/dashboard" className={styles.profileAction}>
                        <svg className={styles.actionIcon} viewBox="0 0 24 24" fill="currentColor">
                          <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                        </svg>
                        Dashboard
                      </Link>
                      <Link to="/profile" className={styles.profileAction}>
                        <svg className={styles.actionIcon} viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                        Profile
                      </Link>
                      <Link to="/membership" className={styles.profileAction}>
                        <svg className={styles.actionIcon} viewBox="0 0 24 24" fill="currentColor">
                          <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"/>
                        </svg>
                        Membership
                      </Link>
                      <button onClick={handleLogout} className={styles.profileAction}>
                        <svg className={styles.actionIcon} viewBox="0 0 24 24" fill="currentColor">
                          <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                        </svg>
                        Logout
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className={styles.authActions}>
              <Link to="/login" className={styles.loginBtn}>
                Login
              </Link>
              <Link to="/register" className={styles.registerBtn}>
                Sign Up
              </Link>
            </div>
          )}

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMobileMenu}
            className={styles.mobileMenuBtn}
            aria-label="Toggle mobile menu"
          >
            <svg className={styles.icon} viewBox="0 0 24 24" fill="currentColor">
              {isMobileMenuOpen ? (
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              ) : (
                <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
              )}
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMobileMenuOpen && (
        <div className={styles.mobileNav}>
          {navigation.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className={`${styles.mobileNavLink} ${isActivePath(item.href) ? styles.active : ''}`}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <span className={styles.navIcon}>{item.icon}</span>
              <span className={styles.navText}>{item.name}</span>
            </Link>
          ))}
          
          {!isAuthenticated && (
            <div className={styles.mobileAuthActions}>
              <Link to="/login" className={styles.mobileLoginBtn} onClick={() => setIsMobileMenuOpen(false)}>
                Login
              </Link>
              <Link to="/register" className={styles.mobileRegisterBtn} onClick={() => setIsMobileMenuOpen(false)}>
                Sign Up
              </Link>
            </div>
          )}
        </div>
      )}

      {/* Overlay for mobile menu */}
      {isMobileMenuOpen && <div className={styles.overlay} onClick={() => setIsMobileMenuOpen(false)} />}
      {isProfileMenuOpen && <div className={styles.overlay} onClick={() => setIsProfileMenuOpen(false)} />}
    </header>
  );
};

export default Header;
