.classes {
  min-height: 100vh;
  background: var(--gray-50);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-6) var(--space-3);
}

/* Header Section */
.header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.125rem;
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Filters */
.filters {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-8);
  justify-content: center;
  flex-wrap: wrap;
}

.filterBtn {
  padding: var(--space-3) var(--space-6);
  border: 2px solid var(--gray-300);
  background: white;
  color: var(--gray-700);
  border-radius: var(--radius-full);
  font-weight: 500;
  transition: all var(--transition-fast);
  cursor: pointer;
  min-height: 48px;
  font-size: 0.875rem;
}

.filterBtn:hover {
  border-color: var(--primary-500);
  color: var(--primary-600);
  transform: translateY(-1px);
}

.filterBtn.active {
  background: var(--primary-500);
  border-color: var(--primary-500);
  color: white;
}

/* Classes Grid */
.classesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--space-6);
}

.classCard {
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
  padding: var(--space-6);
}

.classCard:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.classHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
  gap: var(--space-3);
}

.className {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
  flex: 1;
}

.difficulty {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  flex-shrink: 0;
  background: var(--primary-100);
  color: var(--primary-700);
}

.classDescription {
  font-size: 0.875rem;
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: var(--space-4);
}

.classDetails {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.detail {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.detailLabel {
  font-size: 0.75rem;
  color: var(--gray-500);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.detailValue {
  font-size: 0.875rem;
  color: var(--gray-900);
  font-weight: 600;
}

/* Schedule */
.schedule {
  margin-bottom: var(--space-6);
}

.scheduleTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-3);
}

.session {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-2);
}

.session:last-child {
  margin-bottom: 0;
}

.day {
  font-size: 0.875rem;
  color: var(--gray-700);
  font-weight: 500;
}

.time {
  font-size: 0.875rem;
  color: var(--gray-900);
  font-weight: 600;
}

/* Responsive Design */
@media (min-width: 768px) {
  .container {
    padding: var(--space-8) var(--space-4);
  }
  
  .title {
    font-size: 2.5rem;
  }
  
  .classesGrid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
  
  .filters {
    gap: var(--space-4);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: var(--space-12) var(--space-6);
  }
  
  .title {
    font-size: 3rem;
  }
  
  .classesGrid {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: var(--space-8);
  }
}

@media (max-width: 767px) {
  .classesGrid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .classHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  .difficulty {
    align-self: flex-start;
  }
  
  .classDetails {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
  
  .session {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }
  
  .filters {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: var(--space-2);
  }
  
  .filterBtn {
    flex-shrink: 0;
    padding: var(--space-2) var(--space-4);
    font-size: 0.8rem;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .classCard,
  .filterBtn {
    border: 2px solid #000;
  }
  
  .title {
    -webkit-text-fill-color: #000;
    color: #000;
  }
  
  .difficulty {
    border: 1px solid #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .classCard,
  .filterBtn {
    transition: none;
    transform: none;
  }
  
  .classCard:hover {
    transform: none;
  }
}
