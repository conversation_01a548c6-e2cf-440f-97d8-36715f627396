.sessionTimer {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.sessionTimer.active {
  border-color: #10b981;
  box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.1), 0 2px 4px -1px rgba(16, 185, 129, 0.06);
}

.sessionTimer.paused {
  border-color: #f59e0b;
  box-shadow: 0 4px 6px -1px rgba(245, 158, 11, 0.1), 0 2px 4px -1px rgba(245, 158, 11, 0.06);
}

.timerDisplay {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.timeContent {
  text-align: center;
}

.timeValue {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.timeLabel {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.controlBtn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  min-width: 100px;
  justify-content: center;
}

.primaryBtn {
  background: #667eea;
  color: white;
}

.primaryBtn:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.primaryBtn:active {
  transform: translateY(0);
}

.secondaryBtn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.secondaryBtn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.icon {
  width: 1rem;
  height: 1rem;
}

.sessionInfo {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
}

.infoItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.infoLabel {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.25rem;
}

.infoValue {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
}

.sessionStatus {
  display: flex;
  justify-content: center;
}

.statusIndicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f3f4f6;
  border-radius: 20px;
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.statusDot.active {
  background: #10b981;
}

.statusDot.paused {
  background: #f59e0b;
  animation: none;
}

.statusText {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .sessionTimer {
    padding: 1.5rem;
  }
  
  .timeValue {
    font-size: 1.5rem;
  }
  
  .controls {
    flex-direction: column;
    align-items: center;
  }
  
  .controlBtn {
    width: 100%;
    max-width: 200px;
  }
  
  .sessionInfo {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .infoItem {
    flex-direction: row;
    justify-content: space-between;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .sessionTimer,
  .controlBtn,
  .statusDot {
    transition: none;
    animation: none;
  }
}

/* Focus styles for accessibility */
.controlBtn:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.controlBtn:focus:not(:focus-visible) {
  outline: none;
}
