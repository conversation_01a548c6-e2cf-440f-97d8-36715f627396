/* Global CSS Variables for Design System */
:root {
  /* Typography */
  font-family: system-ui, -apple-system, 'Segoe <PERSON>I', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Color Palette */
  --primary-50: #f0f4ff;
  --primary-100: #e0e9ff;
  --primary-500: #667eea;
  --primary-600: #5a67d8;
  --primary-700: #4c51bf;
  --primary-900: #2d3748;

  --secondary-500: #764ba2;
  --secondary-600: #6b46c1;

  --success-500: #10b981;
  --success-600: #059669;

  --warning-500: #f59e0b;
  --warning-600: #d97706;

  --error-500: #ef4444;
  --error-600: #dc2626;

  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.2s ease-in-out;
  --transition-slow: 0.3s ease-in-out;

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* Font Synthesis */
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--gray-50);
  color: var(--gray-900);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.2;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  margin: 0 0 var(--space-4) 0;
  line-height: 1.6;
}

/* Links */
a {
  color: var(--primary-500);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-600);
}

a:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Buttons */
button {
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  padding: var(--space-3) var(--space-4);
  font-size: 0.875rem;
  font-weight: 500;
  font-family: inherit;
  background-color: var(--primary-500);
  color: white;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 44px; /* Touch-friendly */
}

button:hover:not(:disabled) {
  background-color: var(--primary-600);
  transform: translateY(-1px);
}

button:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Form Elements */
input, textarea, select {
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-300);
  padding: var(--space-3) var(--space-4);
  font-size: 0.875rem;
  font-family: inherit;
  background-color: white;
  transition: border-color var(--transition-fast);
  min-height: 44px; /* Touch-friendly */
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 { gap: var(--space-2); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

.text-center {
  text-align: center;
}

.font-bold {
  font-weight: 700;
}

.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

/* Responsive Design - Mobile First Approach */
/* Base styles are mobile-first, then enhanced for larger screens */

/* Consistent Breakpoints */
/* Mobile: 320px - 767px (default) */
/* Tablet: 768px - 1023px */
/* Desktop: 1024px - 1439px */
/* Large Desktop: 1440px+ */

/* Tablet and up */
@media (min-width: 768px) {
  h1 { font-size: 2.5rem; }
  h2 { font-size: 2rem; }
  h3 { font-size: 1.5rem; }

  .container {
    padding: 0 var(--space-4);
  }
}

/* Desktop and up */
@media (min-width: 1024px) {
  h1 { font-size: 3rem; }
  h2 { font-size: 2.25rem; }
  h3 { font-size: 1.75rem; }
}

/* Large Desktop and up */
@media (min-width: 1440px) {
  .container {
    padding: 0 var(--space-6);
  }
}

/* Mobile-specific overrides */
@media (max-width: 767px) {
  h1 { font-size: 1.875rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
  h4 { font-size: 1.125rem; }

  .container {
    padding: 0 var(--space-3);
  }

  /* Ensure touch-friendly spacing on mobile */
  button, input, textarea, select {
    min-height: 48px; /* Increased for better touch targets */
    font-size: 1rem; /* Prevent zoom on iOS */
  }
}

/* High Contrast Mode for Gym Environment */
@media (prefers-contrast: high) {
  :root {
    --primary-500: #000;
    --primary-600: #000;
    --gray-900: #000;
  }

  button {
    border: 2px solid #000;
  }

  input, textarea, select {
    border: 2px solid #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  html {
    scroll-behavior: auto;
  }
}
