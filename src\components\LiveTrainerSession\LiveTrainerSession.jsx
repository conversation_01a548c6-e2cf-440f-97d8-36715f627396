import { SessionProvider } from '../../contexts/SessionContext';
import { MessageProvider } from '../../contexts/MessageContext';
import SessionTimer from './SessionTimer/SessionTimer';
import TrainerProfile from './TrainerProfile/TrainerProfile';
import MessageOverlay from './MessageOverlay/MessageOverlay';
import styles from './LiveTrainerSession.module.css';

/**
 * Main Live Trainer Session Component
 * Combines all session-related components with context providers
 */
const LiveTrainerSession = () => {
  return (
    <SessionProvider>
      <MessageProvider>
        <div className={styles.liveTrainerSession}>
          <div className={styles.sessionHeader}>
            <h1 className={styles.sessionTitle}>Live Training Session</h1>
            <div className={styles.sessionBadge}>
              <div className={styles.liveDot}></div>
              <span className={styles.liveText}>LIVE</span>
            </div>
          </div>
          
          <div className={styles.sessionContent}>
            <div className={styles.leftPanel}>
              <SessionTimer />
            </div>
            
            <div className={styles.rightPanel}>
              <TrainerProfile />
            </div>
          </div>
          
          <div className={styles.sessionFooter}>
            <div className={styles.sessionInfo}>
              <div className={styles.infoCard}>
                <div className={styles.infoIcon}>🎯</div>
                <div className={styles.infoContent}>
                  <div className={styles.infoLabel}>Today's Goal</div>
                  <div className={styles.infoValue}>Upper Body Strength</div>
                </div>
              </div>
              
              <div className={styles.infoCard}>
                <div className={styles.infoIcon}>🔥</div>
                <div className={styles.infoContent}>
                  <div className={styles.infoLabel}>Intensity</div>
                  <div className={styles.infoValue}>Moderate</div>
                </div>
              </div>
              
              <div className={styles.infoCard}>
                <div className={styles.infoIcon}>⚡</div>
                <div className={styles.infoContent}>
                  <div className={styles.infoLabel}>Energy Level</div>
                  <div className={styles.infoValue}>High</div>
                </div>
              </div>
            </div>
            
            <div className={styles.quickActions}>
              <button className={styles.actionBtn} aria-label="Emergency stop">
                <svg className={styles.actionIcon} viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11H7v-2h10v2z"/>
                </svg>
                Emergency Stop
              </button>
              
              <button className={styles.actionBtn} aria-label="Take a break">
                <svg className={styles.actionIcon} viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                Take Break
              </button>
              
              <button className={styles.actionBtn} aria-label="Need help">
                <svg className={styles.actionIcon} viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
                </svg>
                Need Help
              </button>
            </div>
          </div>
          
          {/* Message overlay is rendered here but positioned fixed */}
          <MessageOverlay />
        </div>
      </MessageProvider>
    </SessionProvider>
  );
};

export default LiveTrainerSession;
