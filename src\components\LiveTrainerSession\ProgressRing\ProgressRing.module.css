.progressRing {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.svg {
  transform: rotate(-90deg);
  overflow: visible;
}

.backgroundCircle {
  opacity: 0.3;
}

.progressCircle {
  transition: stroke-dashoffset 0.3s ease-in-out;
}

.content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 600;
  color: #374151;
}

.percentage {
  font-size: 1.25rem;
  font-weight: 700;
  color: #667eea;
}

/* Responsive sizing */
@media (max-width: 768px) {
  .percentage {
    font-size: 1rem;
  }
}

/* Animation for progress changes */
.progressCircle {
  animation: progressGrow 0.5s ease-out;
}

@keyframes progressGrow {
  from {
    stroke-dashoffset: 100%;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .progressCircle {
    transition: none;
    animation: none;
  }
}
