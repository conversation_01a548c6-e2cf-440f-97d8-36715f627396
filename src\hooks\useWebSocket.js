import { useState, useEffect, useRef, useCallback } from 'react';

/**
 * Mock WebSocket hook for real-time messaging
 * In a real implementation, this would connect to an actual WebSocket server
 * @param {string} url - WebSocket URL (currently unused in mock)
 * @param {Object} options - Configuration options
 * @returns {Object} WebSocket state and methods
 */
export const useWebSocket = (url, options = {}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  
  const reconnectTimeoutRef = useRef(null);
  const messageQueueRef = useRef([]);
  
  // Mock connection simulation
  useEffect(() => {
    // Simulate connection delay
    const connectTimeout = setTimeout(() => {
      setIsConnected(true);
      setConnectionStatus('connected');
      
      // Process any queued messages
      if (messageQueueRef.current.length > 0) {
        messageQueueRef.current.forEach(message => {
          sendMessage(message);
        });
        messageQueueRef.current = [];
      }
    }, 1000);
    
    // Simulate incoming messages from trainer
    const messageInterval = setInterval(() => {
      if (Math.random() > 0.7) { // 30% chance of receiving a message
        const mockMessages = [
          "Great form! Keep it up! 💪",
          "Remember to breathe during the exercise",
          "You're doing amazing! 2 more reps",
          "Perfect technique! 👏",
          "Take a 30-second rest",
          "How are you feeling?",
          "Let's increase the intensity",
          "Focus on your core engagement"
        ];
        
        const randomMessage = mockMessages[Math.floor(Math.random() * mockMessages.length)];
        
        setMessages(prev => [...prev, {
          id: Date.now() + Math.random(),
          text: randomMessage,
          sender: 'trainer',
          timestamp: new Date(),
          status: 'delivered'
        }]);
      }
    }, 15000); // Every 15 seconds
    
    return () => {
      clearTimeout(connectTimeout);
      clearInterval(messageInterval);
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);
  
  // Send message function
  const sendMessage = useCallback((messageText) => {
    if (!isConnected) {
      // Queue message if not connected
      messageQueueRef.current.push(messageText);
      return;
    }
    
    const newMessage = {
      id: Date.now() + Math.random(),
      text: messageText,
      sender: 'client',
      timestamp: new Date(),
      status: 'sending'
    };
    
    setMessages(prev => [...prev, newMessage]);
    
    // Simulate message delivery
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, status: 'delivered' }
            : msg
        )
      );
    }, 500);
    
    // Simulate read receipt
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, status: 'read' }
            : msg
        )
      );
    }, 2000);
  }, [isConnected]);
  
  // Clear messages
  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);
  
  // Simulate typing indicator
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef(null);
  
  const simulateTyping = useCallback(() => {
    setIsTyping(true);
    
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 3000);
  }, []);
  
  // Get unread message count
  const getUnreadCount = useCallback(() => {
    return messages.filter(msg => 
      msg.sender === 'trainer' && msg.status !== 'read'
    ).length;
  }, [messages]);
  
  // Mark messages as read
  const markAsRead = useCallback(() => {
    setMessages(prev => 
      prev.map(msg => 
        msg.sender === 'trainer' 
          ? { ...msg, status: 'read' }
          : msg
      )
    );
  }, []);
  
  // Reconnect function
  const reconnect = useCallback(() => {
    setConnectionStatus('connecting');
    setIsConnected(false);
    
    reconnectTimeoutRef.current = setTimeout(() => {
      setIsConnected(true);
      setConnectionStatus('connected');
    }, 2000);
  }, []);
  
  return {
    isConnected,
    connectionStatus,
    messages,
    isTyping,
    sendMessage,
    clearMessages,
    simulateTyping,
    getUnreadCount,
    markAsRead,
    reconnect
  };
};
