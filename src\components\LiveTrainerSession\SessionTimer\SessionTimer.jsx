import { useSession } from '../../../contexts/SessionContext';
import { formatTime } from '../../../utils/timeUtils';
import ProgressRing from '../ProgressRing/ProgressRing';
import styles from './SessionTimer.module.css';

/**
 * Session Timer Component
 * Displays current session duration with start/pause/stop controls
 */
const SessionTimer = () => {
  const { 
    timer, 
    sessionState, 
    sessionConfig,
    startSession, 
    endSession, 
    pauseSession, 
    resumeSession 
  } = useSession();
  
  const handleStartStop = () => {
    if (!sessionState.isActive) {
      startSession();
    } else {
      endSession();
    }
  };
  
  const handlePauseResume = () => {
    if (timer.isRunning) {
      pauseSession();
    } else if (timer.isPaused) {
      resumeSession();
    }
  };
  
  // Calculate progress if session has a set duration
  const progress = sessionConfig.duration > 0 
    ? (timer.time / sessionConfig.duration) * 100 
    : 0;
  
  const getTimerState = () => {
    if (!sessionState.isActive) return 'inactive';
    if (timer.isPaused) return 'paused';
    if (timer.isRunning) return 'active';
    return 'inactive';
  };
  
  const timerState = getTimerState();
  
  return (
    <div className={`${styles.sessionTimer} ${styles[timerState]}`}>
      <div className={styles.timerDisplay}>
        <ProgressRing
          progress={progress}
          size={140}
          strokeWidth={6}
          color={timerState === 'active' ? '#10b981' : timerState === 'paused' ? '#f59e0b' : '#6b7280'}
          backgroundColor="#e5e7eb"
        >
          <div className={styles.timeContent}>
            <div className={styles.timeValue}>
              {formatTime(timer.time)}
            </div>
            <div className={styles.timeLabel}>
              {timerState === 'active' ? 'Active' : 
              timerState === 'paused' ? 'Paused' : 'Ready'}
            </div>
          </div>
        </ProgressRing>
      </div>
      
      <div className={styles.controls}>
        <button
          onClick={handleStartStop}
          className={`${styles.controlBtn} ${styles.primaryBtn}`}
          aria-label={sessionState.isActive ? 'End Session' : 'Start Session'}
        >
          {sessionState.isActive ? (
            <>
              <svg className={styles.icon} viewBox="0 0 24 24" fill="currentColor">
                <path d="M6 6h12v12H6z"/>
              </svg>
              End
            </>
          ) : (
            <>
              <svg className={styles.icon} viewBox="0 0 24 24" fill="currentColor">
                <path d="M8 5v14l11-7z"/>
              </svg>
              Start
            </>
          )}
        </button>
        
        {sessionState.isActive && (
          <button
            onClick={handlePauseResume}
            className={`${styles.controlBtn} ${styles.secondaryBtn}`}
            aria-label={timer.isRunning ? 'Pause Session' : 'Resume Session'}
          >
            {timer.isRunning ? (
              <>
                <svg className={styles.icon} viewBox="0 0 24 24" fill="currentColor">
                  <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                </svg>
                Pause
              </>
            ) : (
              <>
                <svg className={styles.icon} viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z"/>
                </svg>
                Resume
              </>
            )}
          </button>
        )}
      </div>
      
      {sessionConfig.duration > 0 && (
        <div className={styles.sessionInfo}>
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>Target Duration:</span>
            <span className={styles.infoValue}>{formatTime(sessionConfig.duration)}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>Remaining:</span>
            <span className={styles.infoValue}>
              {formatTime(Math.max(0, sessionConfig.duration - timer.time))}
            </span>
          </div>
        </div>
      )}
      
      {sessionState.isActive && (
        <div className={styles.sessionStatus}>
          <div className={styles.statusIndicator}>
            <div className={`${styles.statusDot} ${styles[timerState]}`}></div>
            <span className={styles.statusText}>
              Session {timerState === 'active' ? 'in progress' : 'paused'}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default SessionTimer;
