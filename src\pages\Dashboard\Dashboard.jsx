import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { progressData, notifications, classes } from '../../data/mockData';
import Button from '../../components/UI/Button/Button';
import Card from '../../components/UI/Card/Card';
import styles from './Dashboard.module.css';

const Dashboard = () => {
  const { user } = useAuth();
  
  const recentWorkouts = progressData.workouts.slice(-3);
  const upcomingClasses = classes.slice(0, 2);
  const recentNotifications = notifications.filter(n => !n.read).slice(0, 3);
  
  const quickActions = [
    {
      title: 'Start Live Session',
      description: 'Join a live training session',
      icon: '🎥',
      href: '/live-sessions',
      color: 'primary'
    },
    {
      title: 'Book a Trainer',
      description: 'Schedule personal training',
      icon: '👨‍💼',
      href: '/trainers',
      color: 'secondary'
    },
    {
      title: 'Join a Class',
      description: 'Browse group fitness classes',
      icon: '🏃‍♀️',
      href: '/classes',
      color: 'success'
    },
    {
      title: 'Track Progress',
      description: 'View your fitness analytics',
      icon: '📊',
      href: '/progress',
      color: 'warning'
    }
  ];

  const stats = [
    {
      label: 'Total Workouts',
      value: user?.stats?.totalWorkouts || 0,
      icon: '🏋️‍♂️',
      change: '+12 this month'
    },
    {
      label: 'Hours Trained',
      value: user?.stats?.totalHours || 0,
      icon: '⏱️',
      change: '+8.5 this month'
    },
    {
      label: 'Current Streak',
      value: `${user?.stats?.currentStreak || 0} days`,
      icon: '🔥',
      change: 'Keep it up!'
    },
    {
      label: 'Membership',
      value: user?.membershipType || 'Basic',
      icon: '💎',
      change: 'Active'
    }
  ];

  return (
    <div className={styles.dashboard}>
      <div className={styles.container}>
        {/* Welcome Section */}
        <div className={styles.welcomeSection}>
          <div className={styles.welcomeContent}>
            <h1 className={styles.welcomeTitle}>
              Welcome back, {user?.firstName}! 👋
            </h1>
            <p className={styles.welcomeSubtitle}>
              Ready to crush your fitness goals today?
            </p>
          </div>
          <div className={styles.welcomeActions}>
            <Button as={Link} to="/live-sessions" size="large">
              Start Workout
            </Button>
            <Button as={Link} to="/progress" variant="outline" size="large">
              View Progress
            </Button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className={styles.statsGrid}>
          {stats.map((stat, index) => (
            <Card key={index} hover className={styles.statCard}>
              <div className={styles.statIcon}>{stat.icon}</div>
              <div className={styles.statContent}>
                <div className={styles.statValue}>{stat.value}</div>
                <div className={styles.statLabel}>{stat.label}</div>
                <div className={styles.statChange}>{stat.change}</div>
              </div>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <section className={styles.section}>
          <h2 className={styles.sectionTitle}>Quick Actions</h2>
          <div className={styles.quickActionsGrid}>
            {quickActions.map((action, index) => (
              <Card key={index} hover className={styles.actionCard}>
                <Link to={action.href} className={styles.actionLink}>
                  <div className={styles.actionIcon}>{action.icon}</div>
                  <div className={styles.actionContent}>
                    <h3 className={styles.actionTitle}>{action.title}</h3>
                    <p className={styles.actionDescription}>{action.description}</p>
                  </div>
                  <div className={styles.actionArrow}>→</div>
                </Link>
              </Card>
            ))}
          </div>
        </section>

        <div className={styles.dashboardGrid}>
          {/* Recent Workouts */}
          <section className={styles.section}>
            <div className={styles.sectionHeader}>
              <h2 className={styles.sectionTitle}>Recent Workouts</h2>
              <Button as={Link} to="/progress" variant="ghost" size="small">
                View All
              </Button>
            </div>
            <Card className={styles.workoutsCard}>
              {recentWorkouts.length > 0 ? (
                <div className={styles.workoutsList}>
                  {recentWorkouts.map((workout, index) => (
                    <div key={index} className={styles.workoutItem}>
                      <div className={styles.workoutIcon}>
                        {workout.type === 'Strength' && '🏋️‍♂️'}
                        {workout.type === 'Cardio' && '🏃‍♂️'}
                        {workout.type === 'HIIT' && '⚡'}
                        {workout.type === 'Yoga' && '🧘‍♀️'}
                      </div>
                      <div className={styles.workoutDetails}>
                        <div className={styles.workoutType}>{workout.type}</div>
                        <div className={styles.workoutMeta}>
                          {workout.duration} min • {workout.calories} cal
                        </div>
                      </div>
                      <div className={styles.workoutDate}>
                        {new Date(workout.date).toLocaleDateString()}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className={styles.emptyState}>
                  <div className={styles.emptyIcon}>🏋️‍♂️</div>
                  <p className={styles.emptyText}>No workouts yet. Start your first session!</p>
                  <Button as={Link} to="/live-sessions">
                    Start Workout
                  </Button>
                </div>
              )}
            </Card>
          </section>

          {/* Upcoming Classes */}
          <section className={styles.section}>
            <div className={styles.sectionHeader}>
              <h2 className={styles.sectionTitle}>Upcoming Classes</h2>
              <Button as={Link} to="/classes" variant="ghost" size="small">
                View All
              </Button>
            </div>
            <Card className={styles.classesCard}>
              {upcomingClasses.length > 0 ? (
                <div className={styles.classesList}>
                  {upcomingClasses.map((classItem) => (
                    <div key={classItem.id} className={styles.classItem}>
                      <div className={styles.classInfo}>
                        <div className={styles.className}>{classItem.name}</div>
                        <div className={styles.classMeta}>
                          {classItem.duration} min • {classItem.difficulty}
                        </div>
                      </div>
                      <div className={styles.classActions}>
                        <Button as={Link} to={`/classes/${classItem.id}`} size="small">
                          Book
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className={styles.emptyState}>
                  <div className={styles.emptyIcon}>🏃‍♀️</div>
                  <p className={styles.emptyText}>No upcoming classes. Browse available classes!</p>
                  <Button as={Link} to="/classes">
                    Browse Classes
                  </Button>
                </div>
              )}
            </Card>
          </section>
        </div>

        {/* Notifications */}
        {recentNotifications.length > 0 && (
          <section className={styles.section}>
            <h2 className={styles.sectionTitle}>Recent Notifications</h2>
            <Card className={styles.notificationsCard}>
              <div className={styles.notificationsList}>
                {recentNotifications.map((notification) => (
                  <div key={notification.id} className={styles.notificationItem}>
                    <div className={styles.notificationIcon}>
                      {notification.type === 'class-reminder' && '📅'}
                      {notification.type === 'achievement' && '🏆'}
                      {notification.type === 'maintenance' && '🔧'}
                    </div>
                    <div className={styles.notificationContent}>
                      <div className={styles.notificationTitle}>
                        {notification.title}
                      </div>
                      <div className={styles.notificationMessage}>
                        {notification.message}
                      </div>
                      <div className={styles.notificationTime}>
                        {new Date(notification.timestamp).toLocaleString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </section>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
